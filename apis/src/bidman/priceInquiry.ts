import { get, post } from '../request'
import {
    IPriceInquiryFilter,
    IPriceInquiry,
    IPageResponse,
    Result,
    IPriceEdit
} from '@haierbusiness-front/common-libs'


export const priceInquiryApi = {
    // 商户询价单-分页
    list: (params: IPriceInquiryFilter): Promise<IPageResponse<IPriceInquiry>> => {
        return get('/mice-bid/api/mice/merchant/price-inquiry/resource-hotel-price-inquiry/page', params)
    },
    // 商户询价单-详情
    get: (params: IPriceInquiryFilter): Promise<IPriceInquiry> => {
        return get('/mice-bid/api/mice/merchant/price-inquiry/resource-hotel-price-inquiry/details', params)
    },
    // 管理的开启权限
    enableSubmitSeason: (params: IPriceInquiry): Promise<Result> => {
        return post('/mice-bid/api/mice/platform/price-inquiry/resource-hotel-price-inquiry/enable-submit-season', params)
    },

    edit: (params: IPriceInquiry): Promise<Result> => {
        return post('/mice-bid/api/mice/merchant/price-inquiry/resource-hotel-price-inquiry/update', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('/mice-bid/api/mice/merchant/price-inquiry/resource-hotel-price-inquiry/delete', { id })
    },

    // 商户询价单-住宿手动询价
    handPriceInquiry: (params: IPriceInquiry): Promise<Result> => {
        return post('/mice-bid/api/mice/platform/price-inquiry/hotel', params)
    },

    // 商户询价单-会场手动询价
    handPriceInquiryMeeting: (params: IPriceInquiry): Promise<Result> => {
        return post('/mice-bid/api/mice/platform/price-inquiry/meeting', params)
    },
    // 商户询价单-确认
    userSubmit: (params: IPriceInquiry): Promise<Result> => {
        return post('/mice-bid/api/mice/platform/price-inquiry/submit', params)
    },
    // 商户询价单-提交
    inquirySheetSubmit: (params: IPriceInquiry): Promise<Result> => {
        return post('/mice-bid/api/mice/merchant/price-inquiry/resource-hotel-price-inquiry/submit', params)
    },

    // 商户询价单-创建询价单时的酒店列表
    listHotel: (params: IPriceInquiryFilter): Promise<IPriceInquiry> => {
        return get('/mice-bid/api/mice/platform/price-inquiry/hotel-list', params)
    },
    // 商户询价单-创建
    hotelCreate: (params: IPriceInquiry): Promise<Result> => {
        return post('/mice-bid/api/mice/platform/price-inquiry/resource-hotel-price-inquiry/create', params)
    },
    // 询价单-授权价格再次填报
    enableSubmitPriceInquiry: (params: IPriceInquiry): Promise<Result> => {
        return post('/mice-bid/api/mice/platform/price-inquiry/resource-hotel-price-inquiry/enable-submit-price-inquiry', params)
    },

    // 商户询价单-详情
    getHotelPriceInquiry: (params: IPriceInquiryFilter): Promise<IPriceInquiry> => {
        return get('/mice-bid/api/mice/merchant/price-inquiry/resource-hotel-price-inquiry/details', params)
    },
    // 商户询价单-开启会场报价
    placeOpen: (params: IPriceInquiry): Promise<Result> => {
        return post('/mice-bid/api/mice/platform/price-inquiry/place-open', params)
    },

    // 产品编辑
    productEdit: (params: IPriceEdit): Promise<Result> => {
        return post('/mice-bid/api/mice/merchant/price-inquiry/product-edit', params)
    },

    // 商户询价单-详情再次提报是回显调用
    getInquiryDetails: (params: IPriceInquiryFilter): Promise<IPriceInquiry> => {
        return get('/mice-bid/api/mice/platform/price-inquiry/resource-hotel-price-inquiry/details', params)
    },
    // 商户询价单-详情再次提报是回显调用
    newGetInquiryDetails: (params: IPriceInquiryFilter): Promise<IPriceInquiry> => {
        return get('/mice-bid/api/mice/merchant/price-inquiry/resource-hotel-price-inquiry/details', params)
    },
    // 商户询价单-获取询价单号
    getAssessmentCode: (params: IPriceInquiryFilter): Promise<IPriceInquiry> => {
        return get('/mice-bid/api/mice/merchant/price-inquiry/resource-hotel-price-inquiry/get-assessment-code', params)
    },
}
