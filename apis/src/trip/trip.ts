import { get, post } from '../request';
import {
  Create_3Res,
  ICreatTrip,
  PageRes,
  IOutPerson,
  DataListRes,
  BudgeQueryRes,
  QueryCityListRes,
  MemberBudgetParams,
  MemberBudgetRes,
  ITrainThree,
  Page_1Res,
  TrainStandardRes,
  ExternalPersonnelReq,
  QueryServiceGuaranteeRes,
  QueryCertificatesRes,
  QueryTripNationalityRes,
} from '@haierbusiness-front/common-libs';
export const tripApi = {
  // 查询当前用户是否有测试权限
  checkBeta: () => {
    return get('trip/api/openapi/checkBeta');
  },
  // 新增出差请假单
  applyCreat: (params: ICreatTrip): Promise<Create_3Res> => {
    return post('trip/api/apply/create', params);
  },
  // 修改出差请假单
  applyUpdate: (params: ICreatTrip): Promise<Create_3Res> => {
    return post('trip/api/apply/update', params);
  },

  // 变更
  applyChange: (params: ICreatTrip): Promise<Create_3Res> => {
    return post('trip/api/apply/change', params);
  },

  // 查询每人明细
  memberAmountList: (params: ICreatTrip): Promise<Create_3Res> => {
    return post('trip/api/budget/memberAmountList', params);
  },
  // 保存出差请假单
  applySubmit: (params: ICreatTrip): Promise<Create_3Res> => {
    return post('trip/api/apply/submit', params);
  },

  // 出差人及外部出行人查询
  page: (): Promise<PageRes> => {
    return get(`trip/api/sy/api/page`);
  },

  // 新增外部出行人
  addOutPerson: (params: IOutPerson): Promise<PageRes> => {
    return post(`trip/api/external/api/sy/create`, params);
  },

  // 获取出行项目列表
  dataList: (): Promise<DataListRes> => {
    return get(`trip/api/product/manage/dataList`);
  },

  // 根据个人或部门查询预算
  budgeQuery: (params: IOutPerson): Promise<BudgeQueryRes> => {
    return post(`trip/api/external/api/budge/query`, params);
  },

  // 根据人查询预算
  queryMemberDetail: (memberCode: string): Promise<PageRes> => {
    return get(`trip/api/external/api/budge/queryMemberDetail?memberCode=${memberCode}`);
  },

  // 查询城市列表

  queryCityList: (): Promise<QueryCityListRes> => {
    return get(`trip/api/external/api/budge/queryCityList`);
  },

  // 计算差旅费
  memberBudget: (params: MemberBudgetParams): Promise<MemberBudgetRes> => {
    return post(`trip/api/budget/memberBudget`, params);
  },

  // 根据单号查询申请单详情
  queryDetailByApplyNo: (id: string): Promise<ICreatTrip> => {
    return get(`trip/api/apply/detail?id=${id}`);
  },
  // 根据单号查询变更单详情
  queryChangeDetailByApplyNo: (id: string): Promise<ICreatTrip> => {
    return get(`trip/api/apply/queryDetailByApplyNo?applyNo=${id}`);
  },

  // 获取出差单列表
  getApplyPage: (params: ICreatTrip): Promise<Page_1Res> => {
    return post(`trip/api/apply/pageNew`, params);
  },

  // 获取出差单列表
  getApplypageList: (params: ICreatTrip): Promise<Page_1Res> => {
    return post(`trip/api/apply/pageList`, params);
  },

  // 作废申请单
  cancelApply: (params: ICreatTrip): Promise<MemberBudgetRes> => {
    return post(`trip/api/apply/cancel`, params);
  },

  // 撤回申请单
  recallApply: (params: ICreatTrip): Promise<MemberBudgetRes> => {
    return post(`trip/api/apply/recall`, params);
  },
  // 撤回变更单
  recallBgApply: (applyNo: any): Promise<MemberBudgetRes> => {
    return post(`trip/api/apply/revokeChangeProcess?applyNo=${applyNo}`);
  },

  // 获取火车三字码
  queryTrainStationList: (cityCode: string): Promise<Array<ITrainThree>> => {
    return get(`trip/api/external/api/budge/queryTrainStationList?cityCode=${cityCode}`);
  },
  // 查询变更记录
  getDataList: (params: ICreatTrip): Promise<Create_3Res> => {
    return post('trip/api/apply/dataList', params);
  },
  // 查询城市地址
  district: (params: any): Promise<Create_3Res> => {
    return get('/city/common/hb/common/api/district/trees?id=1&subdistrict=2', params);
  },

  // 重新审批
  reAudit: (params: ICreatTrip): Promise<MemberBudgetRes> => {
    return post(`trip/api/apply/reAudit`, params);
  },

  // 查询差旅标准
  queryTrainStandard: (params: any): Promise<Array<TrainStandardRes>> => {
    return get('trip/api/external/api/budge/queryTrainStandard', params);
  },

  // 获取外部出行人列表
  getOutPersonList: (params: ExternalPersonnelReq): Promise<Array<TrainStandardRes>> => {
    return get('trip/api/external/api/budge/queryExternalPersonnel', params);
  },

  // 查询服务等级
  queryServiceGuarantee: (): Promise<Array<QueryServiceGuaranteeRes>> => {
    return get('trip/api/apply/queryServiceGuarantee');
  },
  // 查询证件类型
  queryCertificates: (): Promise<Array<QueryCertificatesRes>> => {
    return get('trip/api/apply/queryCertificates');
  },

  // 查询国籍信息

  queryTripNationality: (): Promise<QueryTripNationalityRes> => {
    return get('trip/api/external/api/queryTripNationality');
  },

  // 模糊查询国籍信息
  queryTripNationalityByKeyword: (keyword: string): Promise<QueryTripNationalityRes> => {
    return get(`trip/api/external/api/queryTripNationalityByKeyword?keyword=${keyword}`);
  },

  // 行程确认
  tripFinish: (params: Array<any>): Promise<any> => {
    return post(`trip/api/apply/tripFinish`, params);
  },
  // 查询行程确认数据

  getRealTrip: (params: Array<any>): Promise<any> => {
    return get(`trip/api/apply/getRealTrip`, params);
  },
};
