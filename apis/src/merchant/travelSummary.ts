import { download, get, post, originalPost,downloadPost } from '../request'
import { 
    IDiscountFilter, 
    IDiscount,
    IPageResponse, 
    ITriveFilter,
    Result,
    TravelSumReportPageRes,
    TravelSumReportPageReq,
    CreateTravelSumReportParams
 } from '@haierbusiness-front/common-libs'


export const travelSummaryApi = {

    getReport: (params: ITriveFilter): Promise<any> => {
        return get('/data/api/travel_sum/getReport', params)
    },
    getUserAvatar: (params: ITriveFilter): Promise<IPageResponse<Result>> => {
        return get('/data/api/travel_sum/getUserAvatar', params)
    },
    // 是否已读
    readNotify: (params: ITriveFilter): Promise<IPageResponse<Result>> => {
        return get('/data/api/travel_sum/readNotify', params)
    },
    getReportDetail: (params: ITriveFilter): Promise<any> => {
        return get('/data/api/travel_sum/getReportDetail', params)
    },

    // 后台管理接口-----

    // 获取季度报告列表分页接口
    list: (params: TravelSumReportPageReq): Promise<IPageResponse<TravelSumReportPageRes>> => {
        return get('/data/api/travel_sum/page', params)
    },

    // 创建季度报告接口
    create: (params: CreateTravelSumReportParams): Promise<any> => {
        return post('/data/api/travel_sum/create', params)
    },

    // 停止发送
    stop: (params: any): Promise<IPageResponse<any>> => {
        return get('/data/api/travel_sum/stopSending', params)
    },
    // 确认报告
    confirmReport: (params: any): Promise<IPageResponse<any>> => {
        return get('/data/api/travel_sum/confirmReport', params)
    },
    // 取消发送
    cancelReport: (params: any): Promise<IPageResponse<any>> => {
        return get('/data/api/travel_sum/cancelReport', params)
    },

    // 撤回消息
    revoke: (params: any): Promise<IPageResponse<any>> => {
        return get('/data/api/travel_sum/revokeMessage', params)
    },

    // 详情
    detail: (params: any): Promise<IPageResponse<any>> => {
        return get('/data/api/travel_sum/getReport', params)
    },

    // 报告阅读明细分页接口
    travelSumReportUserPage: (params: any): Promise<IPageResponse<any>> => {
        return get('/data/api/travel_sum/travelSumReportUserPage', params)
    },

    // 根据时间范围查询预计发送人数
    getCreateEstimatedPeopleNumber: (params: any): Promise<IPageResponse<any>> => {
        return post('/data/api/travel_sum/getCreateEstimatedPeopleNumber', params)
    },

    // 报告阅读明细导出接口
    exportSumReportUser: (params: any): Promise<IPageResponse<any>> => {
        return downloadPost('/data/api/travel_sum/exportSumReportUser', params)
    },

    // 导出时间范围查询预计发送人数
    exportEstimatedPeopleNumberExcel: (params: any): Promise<IPageResponse<any>> => {
        return downloadPost('/data/api/travel_sum/exportEstimatedPeopleNumberExcel', params)
    },

    // 报告明细导出接口
    exportSumReportDetail: (params: any): Promise<IPageResponse<any>> => {
        return downloadPost('/data/api/travel_sum/exportSumReportDetail', params)
    },
    

    travelSumReportDetailPage: (params: any): Promise<IPageResponse<any>> => {
        return get('/data/api/travel_sum/travelSumReportDetailPage', params)
    },
}
