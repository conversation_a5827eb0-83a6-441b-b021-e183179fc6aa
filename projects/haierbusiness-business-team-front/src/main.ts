
import { createApp } from 'vue'
import router from './router';
import './assets/css/main.less'
import './assets/css/font.css'
import App from './App.vue'
import globalPinia from "@haierbusiness-front/utils/src/store/store"
import { setGlobalOptions } from 'vue-request';
// import './utils/rem'
import Antd from 'ant-design-vue';
import Vant from 'vant';

import 'ant-design-vue/dist/reset.css';
import 'vant/lib/index.css'
import {autoCheckVersion} from "@haierbusiness-front/composables/src/checkVersion";

autoCheckVersion();
setGlobalOptions({
    manual: true,
    pagination: {
        currentKey: 'pageNum',
        pageSizeKey: 'pageSize',
    },
});



router.then(it => {
    const app = createApp(App);
    
    app.use(globalPinia as any)
        .use(it  as any)
        .use(Antd)
        .use(Vant)
        .mount('#app');

    app.config.globalProperties.$hbRouter = it
})



