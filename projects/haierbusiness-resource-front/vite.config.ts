import { loadEnv } from 'vite'

import vue from '@vitejs/plugin-vue'
import path from 'path';
import {writeFileSync} from "fs";

const CWD = process.cwd();

export default ({ mode }) => {
  const { VITE_BASE_URL } = loadEnv(mode, CWD);
  // 生成版本信息插件
  const generateVersionPlugin = {
    name: 'generate-version',
    // 仅在构建时执行
    apply: 'build',
    closeBundle() {
      // 生成版本文件
      const versionInfo = {
        // 使用时间戳作为版本号
        version: Date.now(),
        buildTime: new Date().toISOString(),
        mode: mode
      };

      writeFileSync(
          path.resolve(__dirname, 'dist/version.json'),
          JSON.stringify(versionInfo, null, 2)
      );

    }
  };
  return {
    base: VITE_BASE_URL,
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './'),
        '@': path.resolve(__dirname, './src'),
      },
    },
    plugins: [vue(),generateVersionPlugin],
    build:{
      target:['es2015']
    },
    server: {
      port: 5190,
      proxy: {
        "/hb/hotel-mapping": {
          target: "https://businessmanagement-test.haier.net/hbweb/hotel-mapping/hb/hotel-mapping",
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`/hb/hotel-mapping`), ''),
        },
        "/hb/common": {
          target: "https://businessmanagement-test.haier.net/hbweb/hotel-mapping/hb/common",
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`/hb/hotel-mapping`), ''),
        },
        "/hb": {
          target: "https://businessmanagement-test.haier.net/hbweb/hotel-analysis/hb",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/hb/, ""),
        },
      },
      
    }
  }
}
