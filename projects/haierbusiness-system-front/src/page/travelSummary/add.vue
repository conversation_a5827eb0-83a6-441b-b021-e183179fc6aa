<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  InputSearch as hInputSearch,
  message,
  TableProps,
  InputNumber as hInputNumber
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { DownOutlined, ExclamationCircleOutlined, PlusOutlined, UploadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue';
import { apiLogApi } from '@haierbusiness-front/apis';
import {
  IApiLogRequest,
  ReportScopeConstant,
  ReportStatusConstant,
  TravelSumReportPageReq
} from '@haierbusiness-front/common-libs';
import { travelSummaryApi } from "@haierbusiness-front/apis";

import { errorModal, routerParam } from '@haierbusiness-front/utils';
import Eloading from '@haierbusiness-front/components/loading/Eloading.vue';
import dayjs, { Dayjs } from 'dayjs';
import { computed, onMounted, ref, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useRouter } from "vue-router";
import { getEnumOptions } from '@haierbusiness-front/utils';

const router = useRouter()

const addDialog = ref(false)
const travelSumReportForm = ref<TravelSumReportPageReq>({
  year: dayjs().format('YYYY')
})
const rules: Record<string, Rule[]> = {
  reportTitle: [{ required: true, message: '请输入报告标题', trigger: 'change' }],
  sendTime: [{ required: true, message: '请选择发送时间', trigger: 'change' }],
  sendMethod: [{ required: true, message: '请选择发送方式', trigger: 'change' }],
  reportScope: [{ required: true, message: '请选择报告范围', trigger: 'change' }],
  cycleTimeRange: [{ required: true, message: '请选择时间周期	', trigger: 'change' }],
  year: [{ required: true, message: '请选择年度报告', trigger: 'change' }],
  averageDiscount: [{ required: true, message: '请输入机票平均折扣', trigger: 'change' }],
};
const layout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 8 },
};


const reportScopeConstantList = computed(() => {
  return getEnumOptions(ReportScopeConstant, false);
});
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const formRef = ref();

const onSubmitLoading = ref(false)
const onSubmit = () => {
  formRef.value
    .validate()
    .then(() => {
      onSubmitLoading.value = true
      if (travelSumReportForm.value.sendMethod == 1) {
        travelSumReportForm.value.sendTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
      }
      travelSummaryApi.create(travelSumReportForm.value).then(res => {
        message.success("新增成功")
        onSubmitLoading.value = false
        router.push('/travelSummary/list')

      }).catch((err: any) => {
        onSubmitLoading.value = false
        message.error("新增失败")
      })

    })
    .catch((error: any) => {

    });
};

const cancle = () => {
  router.push('/travelSummary/list')
}
// 根据时间范围查询预计发送人数
const getCreateEstimatedPeopleNumber = () => {
  onSubmitLoading.value = true
  travelSummaryApi.getCreateEstimatedPeopleNumber({
    cycleTimeRange: travelSumReportForm.value?.cycleTimeRange
  }).then(res => {
    onSubmitLoading.value = false
    travelSumReportForm.value.settlementAccount = res
  }).catch(() => {
    onSubmitLoading.value = false
  })

}
// watch(
//   () => travelSumReportForm.value?.cycleTimeRange,
//   (val:any) => {
//     if (val?.length > 0) {
//       getCreateEstimatedPeopleNumber()

//     }else {
//       travelSumReportForm.value.settlementAccount = undefined
//     }
//   }
// )

// 报告范围		
// YEAR(1, "年度"),
// FIRST_HALF_YEAR(2, "上半年"),
// SECOND_HALF_YEAR(3, "下半年"),
// FIRST_QUARTER(4, "第一季度"),
// SECOND_QUARTER(5, "第二季度"),
// THIRD_QUARTER(6, "第三季度"),
// FOURTH_QUARTER(7, "第四季度"),
// January(8, "一月"),
// February(9, "二月"),
// March(10, "三月"),
// April(11, "四月"),
// May(12, "五月"),
// June(13, "六月"),
// July(14, "七月"),
// August(15, "八月"),
// September(16, "九月"),
// October(17, "十月月"),
// November(18, "十一月"),
// December(19, "十二月"),
const changeReportScope = (val: string) => {
  switch (val) {
    // 年度
    case "YEAR":
      travelSumReportForm.value.cycleTimeRange = [dayjs(travelSumReportForm.value.year).startOf('year').format('YYYY-MM-DD HH:mm:ss'), dayjs(travelSumReportForm.value.year).endOf('year').format('YYYY-MM-DD HH:mm:ss')]
      break;
    // 上半年
    case "FIRST_HALF_YEAR":
      travelSumReportForm.value.cycleTimeRange = [dayjs(travelSumReportForm.value.year).startOf('year').format('YYYY-MM-DD HH:mm:ss'), dayjs(travelSumReportForm.value.year).month(5).endOf('month').format('YYYY-MM-DD HH:mm:ss')]
      break;
    // 下半年
    case "SECOND_HALF_YEAR":
      travelSumReportForm.value.cycleTimeRange = [dayjs(travelSumReportForm.value.year).month(6).startOf('month').format('YYYY-MM-DD HH:mm:ss'), dayjs(travelSumReportForm.value.year).endOf('year').format('YYYY-MM-DD HH:mm:ss')]
      break;
    // 第一季度
    case "FIRST_QUARTER":
      travelSumReportForm.value.cycleTimeRange = [dayjs(travelSumReportForm.value.year).startOf('year').format('YYYY-MM-DD HH:mm:ss'), dayjs(travelSumReportForm.value.year).month(2).endOf('month').format('YYYY-MM-DD HH:mm:ss')]
      break;
    // 第二季度
    case "SECOND_QUARTER":
      travelSumReportForm.value.cycleTimeRange = [dayjs(travelSumReportForm.value.year).month(3).startOf('month').format('YYYY-MM-DD HH:mm:ss'), dayjs(travelSumReportForm.value.year).month(5).endOf('month').format('YYYY-MM-DD HH:mm:ss')]
      break;
    // 第三季度
    case "THIRD_QUARTER":
      travelSumReportForm.value.cycleTimeRange = [dayjs(travelSumReportForm.value.year).month(6).startOf('month').format('YYYY-MM-DD HH:mm:ss'), dayjs(travelSumReportForm.value.year).month(8).endOf('month').format('YYYY-MM-DD HH:mm:ss')]
      break;
    // 第四季度
    case "FOURTH_QUARTER":
      travelSumReportForm.value.cycleTimeRange = [dayjs(travelSumReportForm.value.year).month(9).startOf('month').format('YYYY-MM-DD HH:mm:ss'), dayjs(travelSumReportForm.value.year).endOf('year').format('YYYY-MM-DD HH:mm:ss')]
      break;


    // 一月
    case "January":
      travelSumReportForm.value.cycleTimeRange = [dayjs(travelSumReportForm.value.year).startOf('year').format('YYYY-MM-DD HH:mm:ss'), dayjs(travelSumReportForm.value.year).month(0).endOf('month').format('YYYY-MM-DD HH:mm:ss')]
      break;
    // 二月
    case "February":
      travelSumReportForm.value.cycleTimeRange = [dayjs(travelSumReportForm.value.year).month(1).startOf('month').format('YYYY-MM-DD HH:mm:ss'), dayjs(travelSumReportForm.value.year).month(1).endOf('month').format('YYYY-MM-DD HH:mm:ss')]
      break;

    // 三月
    case "March":
      travelSumReportForm.value.cycleTimeRange = [dayjs(travelSumReportForm.value.year).month(2).startOf('month').format('YYYY-MM-DD HH:mm:ss'), dayjs(travelSumReportForm.value.year).month(2).endOf('month').format('YYYY-MM-DD HH:mm:ss')]
      break;

    // 四月
    case "April":
      travelSumReportForm.value.cycleTimeRange = [dayjs(travelSumReportForm.value.year).month(3).startOf('month').format('YYYY-MM-DD HH:mm:ss'), dayjs(travelSumReportForm.value.year).month(3).endOf('month').format('YYYY-MM-DD HH:mm:ss')]
      break;

    case "May":
      travelSumReportForm.value.cycleTimeRange = [dayjs(travelSumReportForm.value.year).month(4).startOf('month').format('YYYY-MM-DD HH:mm:ss'), dayjs(travelSumReportForm.value.year).month(4).endOf('month').format('YYYY-MM-DD HH:mm:ss')]
      break;

    case "June":
      travelSumReportForm.value.cycleTimeRange = [dayjs(travelSumReportForm.value.year).month(5).startOf('month').format('YYYY-MM-DD HH:mm:ss'), dayjs(travelSumReportForm.value.year).month(5).endOf('month').format('YYYY-MM-DD HH:mm:ss')]
      break;


    case "July":
      travelSumReportForm.value.cycleTimeRange = [dayjs(travelSumReportForm.value.year).month(6).startOf('month').format('YYYY-MM-DD HH:mm:ss'), dayjs(travelSumReportForm.value.year).month(6).endOf('month').format('YYYY-MM-DD HH:mm:ss')]
      break;


    case "August":
      travelSumReportForm.value.cycleTimeRange = [dayjs(travelSumReportForm.value.year).month(7).startOf('month').format('YYYY-MM-DD HH:mm:ss'), dayjs(travelSumReportForm.value.year).month(7).endOf('month').format('YYYY-MM-DD HH:mm:ss')]
      break;

    case "September":
      travelSumReportForm.value.cycleTimeRange = [dayjs(travelSumReportForm.value.year).month(8).startOf('month').format('YYYY-MM-DD HH:mm:ss'), dayjs(travelSumReportForm.value.year).month(8).endOf('month').format('YYYY-MM-DD HH:mm:ss')]
      break;

    case "October":
      travelSumReportForm.value.cycleTimeRange = [dayjs(travelSumReportForm.value.year).month(9).startOf('month').format('YYYY-MM-DD HH:mm:ss'), dayjs(travelSumReportForm.value.year).month(9).endOf('month').format('YYYY-MM-DD HH:mm:ss')]
      break;

    case "November":
      travelSumReportForm.value.cycleTimeRange = [dayjs(travelSumReportForm.value.year).month(10).startOf('month').format('YYYY-MM-DD HH:mm:ss'), dayjs(travelSumReportForm.value.year).month(10).endOf('month').format('YYYY-MM-DD HH:mm:ss')]
      break;

    case "December":
      travelSumReportForm.value.cycleTimeRange = [dayjs(travelSumReportForm.value.year).month(11).startOf('month').format('YYYY-MM-DD HH:mm:ss'), dayjs(travelSumReportForm.value.year).month(11).endOf('month').format('YYYY-MM-DD HH:mm:ss')]
      break;
    default:
      travelSumReportForm.value.cycleTimeRange = []
      break;
  }
}

const changeYear = (val: any) => {
  changeReportScope(travelSumReportForm.value.reportScope)
}

const downloadLoading = ref(false)
const download = () => {
  downloadLoading.value = true
  if (travelSumReportForm.value.cycleTimeRange && travelSumReportForm.value.cycleTimeRange?.length > 1) {

    travelSummaryApi.exportEstimatedPeopleNumberExcel({ cycleTimeRange: travelSumReportForm.value.cycleTimeRange }).then(res => {
      downloadLoading.value = false
    }).catch(err => {
      downloadLoading.value = false
    })
  } else {
    downloadLoading.value = false
    message.error('请先选择时间周期!')
  }
}


</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 40px;overflow: auto;">
    <h2 style="text-align: center; margin-bottom: 50px;">差旅报告管理</h2>
    <h-form ref="formRef" :model="travelSumReportForm" :rules="rules" v-bind="layout">
      <h-form-item label="报告标题" name="reportTitle">
        <h-input v-model:value="travelSumReportForm.reportTitle" placeholder="报告标题" autocomplete="off" />
      </h-form-item>
      <h-form-item label="报告年度" name="year">
        <h-date-picker @change="changeYear" style="width: 100%" :allow-clear="false" value-format="YYYY"
          v-model:value="travelSumReportForm.year" picker="year" />
      </h-form-item>

      <h-form-item label="报告范围" name="reportScope">
        <h-select ref="reportScope" v-model:value="travelSumReportForm.reportScope" @change="changeReportScope"
          style="width: 100%" allow-clear show-search placeholder="报告范围" :options="reportScopeConstantList"
          :filterOption="filterOption">
        </h-select>
      </h-form-item>

      <h-form-item label="机票平均折扣" name="averageDiscount">
        <h-input-number style="width: 100%" placeholder="机票平均折扣" :precision="3"
          v-model:value="travelSumReportForm.averageDiscount" :min="0.001" :max="0.999" />

      </h-form-item>

      <h-form-item label="发送方式" name="sendMethod">
        <a-radio-group v-model:value="travelSumReportForm.sendMethod">
          <a-radio :value="1">立即发送</a-radio>
          <a-radio :value="2">定时发送</a-radio>
        </a-radio-group>
      </h-form-item>

      <h-form-item v-if="travelSumReportForm?.sendMethod == 2" label="发送时间" name="sendTime">
        <h-date-picker v-model:value="travelSumReportForm.sendTime" show-time value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%" />
      </h-form-item>

      <h-form-item label="时间周期" name="cycleTimeRange">
        <h-range-picker v-model:value="travelSumReportForm.cycleTimeRange" show-time value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%" />
      </h-form-item>

      <!-- <h-form-item  label="预计接收人" name="settlementAccount">
          
          <h-input-search
            v-model:value="travelSumReportForm.settlementAccount"
            placeholder="预计接收人"
            autocomplete="off"
            readonly
            disabled
            @search="onSearch"
          >
            <template #enterButton>
              <h-button @click="download" :loading="downloadLoading">下载详情</h-button>
            </template>
          </h-input-search>
        </h-form-item> -->


      <h-form-item :wrapper-col="{ span: 18, offset: 9 }">
        <h-button type="primary" :loading="onSubmitLoading" @click="onSubmit">生成</h-button>
        <h-button style="margin-left: 10px" @click="cancle">返回</h-button>
      </h-form-item>
    </h-form>

  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
