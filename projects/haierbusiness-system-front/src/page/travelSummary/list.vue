<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { DownOutlined, ExclamationCircleOutlined, PlusOutlined, UploadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue';
import { apiLogApi } from '@haierbusiness-front/apis';
import {
  IApiLogRequest,
  ReportScopeConstant,
  ReportStatusConstant,
  TravelSumReportPageReq
} from '@haierbusiness-front/common-libs';
import { travelSummaryApi } from "@haierbusiness-front/apis";

import { errorModal, routerParam } from '@haierbusiness-front/utils';
import Eloading from '@haierbusiness-front/components/loading/Eloading.vue';
import dayjs, { Dayjs } from 'dayjs';
import { computed, onMounted, ref, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useRouter } from "vue-router";
import { getEnumOptions } from '@haierbusiness-front/utils';



const router = useRouter()
const columns: ColumnType[] = [
  {
    title: '经办人',
    dataIndex: 'createName',
    width: '240px',
    align: 'center',
    ellipsis: true,
    fixed: 'left',
  },

  {
    title: '发送时间',
    dataIndex: 'sendTime',
    width: '220px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '报告标题',
    dataIndex: 'reportTitle',
    width: '240px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '报告范围',
    dataIndex: 'reportScope',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '机票平均折扣',
    dataIndex: 'averageDiscount',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '时间周期',
    dataIndex: 'cycleTimeStart',
    width: '380px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '阅读情况',
    dataIndex: 'totalCount',
    width: '200px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '通知状态',
    dataIndex: 'status',
    width: '200px',
    align: 'center',
    ellipsis: true
  },

  {
    title: "操作",
    dataIndex: "_operator",
    width: "300px",
    fixed: "right",
    align: "center",
  },


];
const searchParam = ref<IApiLogRequest>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(travelSummaryApi.list);

const reset = () => {
  searchParam.value = {}
  handleTableChange({ current: 1, pageSize: 10 })
}
const goToAdd = () => {
  router.push('/travelSummary/add')
}

const statusList = computed(() => {
  return getEnumOptions(ReportStatusConstant, true);
});

// 
const reportScopeConstantList = computed(() => {
  return getEnumOptions(ReportScopeConstant, false);
});
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};


const dataSource = computed(() => {
  return data.value?.records || []
});

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const goToDetail = (row: any) => {
  router.push(`/travelSummary/detail?id=${row.travelSumReportId}`)
}

const stopSendMessage = (row: any) => {

  loading.value = true
  travelSummaryApi.stop({ travelSumReportId: row.travelSumReportId }).then(res => {
    loading.value = false
    message.success('停止成功');
    handleTableChange({ current: pagination.current, pageSize: pagination.pageSize })
  }).catch(() => {
    loading.value = false
  })
}
// 确认发送
const confirmSend = (row: any) => {

  loading.value = true
  travelSummaryApi.confirmReport({ travelSumReportId: row.travelSumReportId }).then(res => {
    loading.value = false
    message.success('确认成功');
    handleTableChange({ current: pagination.current, pageSize: pagination.pageSize })
  }).catch(() => {
    loading.value = false
  })
}
// 取消发送
const cancelSend = (row: any) => {

  loading.value = true
  travelSummaryApi.cancelReport({ travelSumReportId: row.travelSumReportId }).then(res => {
    loading.value = false
    message.success('取消成功');
    handleTableChange({ current: pagination.current, pageSize: pagination.pageSize })
  }).catch(() => {
    loading.value = false
  })
}

const revokeMessage = (row: any) => {
  loading.value = true
  travelSummaryApi.revoke({ travelSumReportId: row.travelSumReportId }).then(res => {
    loading.value = false
    message.success('撤回成功');
    handleTableChange({ current: pagination.current, pageSize: pagination.pageSize })
  }).catch(() => {
    loading.value = false
  })
}

onMounted(() => {
  listApiRun({ pageNum: 1, pageSize: 10 });
});
</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">

    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="createBy">经办人工号：</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="经办人工号" id="createBy" v-model:value="searchParam.createBy" autocomplete="off"
              allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="createName">经办人名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="经办人名称" id="createName" v-model:value="searchParam.createName" autocomplete="off"
              allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="reportTitle">报告标题：</label>
          </h-col>
          <h-col :span="4">
            <h-input placeholder="报告标题" id="reportTitle" v-model:value="searchParam.reportTitle" autocomplete="off"
              allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="reportScope">报告范围:</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="reportScope" v-model:value="searchParam.reportScope" style="width: 100%" allow-clear
              show-search placeholder="报告范围" :options="reportScopeConstantList" :filterOption="filterOption">
            </h-select>
          </h-col>


        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="status">通知状态:</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="status" v-model:value="searchParam.status" style="width: 100%" allow-clear show-search
              placeholder="通知状态" :options="statusList" :filterOption="filterOption">
            </h-select>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="sendTimeStart">开始发送时间:</label>
          </h-col>
          <h-col :span="4">
            <h-date-picker v-model:value="searchParam.sendTimeStart" format="YYYY-MM-DD HH:mm:ss"
              :show-time="{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }" value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%" />
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="sendTimeEnd">截止发送时间:</label>
          </h-col>
          <h-col :span="4">
            <h-date-picker v-model:value="searchParam.sendTimeEnd" format="YYYY-MM-DD HH:mm:ss"
              :show-time="{ defaultValue: dayjs('23:59:59', 'HH:mm:ss') }" value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%" />
          </h-col>

          <!-- <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="cycleTimeStart">开始时间周期:</label>
          </h-col>
          <h-col :span="4">
              <h-date-picker v-model:value="searchParam.cycleTimeStart" format="YYYY-MM-DD HH:mm:ss" :show-time="{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
          </h-col>
 -->

        </h-row>
        <!-- 
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="cycleTimeEnd">截止时间周期:</label>
          </h-col>
          <h-col :span="4">
              <h-date-picker v-model:value="searchParam.cycleTimeEnd" format="YYYY-MM-DD HH:mm:ss" :show-time="{ defaultValue: dayjs('23:59:59', 'HH:mm:ss') }" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
          </h-col>
        </h-row> -->

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
            <h-button type="primary" @click="goToAdd">
              新增差旅报告
            </h-button>
          </h-col>
          <h-col :span="12" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'status'">
              {{ ReportStatusConstant[record.status] }}
            </template>

            <template v-if="column.dataIndex === 'createName'">
              {{ `${record.createName}(${record.createBy})` }}
            </template>

            <template v-if="column.dataIndex === 'reportScope'">
              {{ ReportScopeConstant[record.reportScope] }}
            </template>

            <template v-if="column.dataIndex === 'cycleTimeStart'">
              {{ `${record.cycleTimeStart} - ${record.cycleTimeEnd}` }}
            </template>

            <template v-if="column.dataIndex === 'totalCount'">
              {{ record.status != '10' && record.status != '15' ? `${record.readCount} / ${record.totalCount}` : '计算中' }}
            </template>

            <template v-if="column.dataIndex === '_operator'">
              <a-popconfirm title="确认发送吗?" ok-text="确认" cancel-text="取消" @confirm="confirmSend(record)">
                <h-button type="link" v-if="record.status == 16">确认发送</h-button>
              </a-popconfirm>


              <a-popconfirm title="确认取消吗?" ok-text="确认" cancel-text="取消" @confirm="cancelSend(record)">
                <h-button type="link" v-if="record.status == 16">取消发送</h-button>
              </a-popconfirm>


              <h-button type="link" @click="goToDetail(record)">查看详情</h-button>
              <!-- // 撤回消息 status 发送完成 停止发送 40、70

                // 停止发送 发送中 30 -->
              <h-button type="link" v-if="record.status == 30" @click="stopSendMessage(record)">停止发送</h-button>
              <h-button type="link" v-if="record.status == 40 || record.status == 70"
                @click="revokeMessage(record)">撤回消息</h-button>
            </template>

          </template>
        </h-table>
      </h-col>
    </h-row>

  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
