<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { DownOutlined, ExclamationCircleOutlined, PlusOutlined, UploadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue';
import { apiLogApi } from '@haierbusiness-front/apis';
import {
  IApiLogRequest,
  ReportScopeConstant,
  ReportStatusConstant,
  TravelSumReportPageReq
} from '@haierbusiness-front/common-libs';
import { travelSummaryApi } from "@haierbusiness-front/apis";

import { errorModal, routerParam } from '@haierbusiness-front/utils';
import Eloading from '@haierbusiness-front/components/loading/Eloading.vue';
import dayjs, { Dayjs } from 'dayjs';
import { computed, onMounted, ref, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useRouter } from "vue-router";
import { getEnumOptions, getCurrentRoute } from '@haierbusiness-front/utils';

const router = useRouter()
const currentRouter = ref()
const route = ref(getCurrentRoute());


const id = route.value?.query?.id;
const detail = ref<any>({});

const getDetail = (id: any) => {
  travelSummaryApi.detail({ travelSumReportId: id }).then(res => {
    detail.value = res
  })
}

const goToReadDetail = (detail: any) => {
  router.push({
    path: '/travelSummary/readDetail',
    query: {
      id: detail?.travelSumReportId,
      totalCount: detail?.totalCount,
      readCount: detail?.readCount,
      sendTime: detail?.sendTime
    }
  })
}
// 报告明细
const goToReportDetail = (detail: any) => {
  router.push({
    path: '/travelSummary/reportDetail',
    query: {
      id: detail?.travelSumReportId,
      totalCount: detail?.totalCount,
      gmtCreate: detail?.gmtCreate
    }
  })
}

onMounted(async () => {
  currentRouter.value = await router

});
watch(
  () => id,
  (val: string) => {
    getDetail(val);
  },
  {
    immediate: true,
  },
);
</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 60px 60px 0px 60px;overflow: auto;">
    <h-row justify="space-between" style="margin-bottom: 20px;">
      <h-col style="font-size: 18px;">差旅报告管理</h-col>
      <h-col><h-button type="link" @click="currentRouter.back(-1)">返回</h-button></h-col>
    </h-row>
    <div>

      <h-descriptions :labelStyle="{ width: '100px', }" :column="1">
        <h-descriptions-item label="报告标题">{{ detail?.reportTitle }}</h-descriptions-item>
        <h-descriptions-item label="发送人数">
          <div style="display: flex; align-items: center;">
            <div style="margin-right: 10px;">{{ `${detail?.readCount || 0} / ${detail?.totalCount || 0}` }}</div>
            <div @click="goToReadDetail(detail)" style="cursor: pointer; margin-right: 10px; color: #1677ff;">阅读明细</div>

            <div @click="goToReportDetail(detail)" style="cursor: pointer; color: #1677ff;">报告明细</div>
          </div>
        </h-descriptions-item>
        <h-descriptions-item label="报告年度">{{ detail?.year }}</h-descriptions-item>
        <h-descriptions-item label="报告范围">{{ detail?.reportScopeName }}</h-descriptions-item>
        <h-descriptions-item label="机票平均折扣">{{ detail?.averageDiscount }}</h-descriptions-item>
        <h-descriptions-item label="时间周期">{{ `${detail?.cycleTimeStart} - ${detail?.cycleTimeEnd}`
        }}</h-descriptions-item>
        <h-descriptions-item label="发送状态">{{ ReportStatusConstant[detail?.status] }}</h-descriptions-item>
        <h-descriptions-item label="发送时间">{{ detail?.sendTime }}</h-descriptions-item>
        <h-descriptions-item label="经办人">{{ `${detail?.createName}(${detail?.createBy})` }}</h-descriptions-item>
      </h-descriptions>
    </div>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
