<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  RadioButton as hRadioButton
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { DownOutlined, ExclamationCircleOutlined, PlusOutlined, UploadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue';
import { apiLogApi } from '@haierbusiness-front/apis';
import {
  IApiLogRequest,
  ReportScopeConstant,
  ReportStatusConstant,
  TravelSumReportPageReq
} from '@haierbusiness-front/common-libs';
import { travelSummaryApi } from "@haierbusiness-front/apis";

import { errorModal, routerParam } from '@haierbusiness-front/utils';
import Eloading from '@haierbusiness-front/components/loading/Eloading.vue';
import dayjs, { Dayjs } from 'dayjs';
import { computed, onMounted, ref, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useRouter } from "vue-router";
import { getEnumOptions, getCurrentRoute } from '@haierbusiness-front/utils';

const router = useRouter()
const currentRouter = ref()
const route = ref(getCurrentRoute());


const id = route.value?.query?.id;
const gmtCreate = route.value?.query?.gmtCreate;
const totalCount = route.value?.query?.totalCount;

const readFlag = ref('');
const downloading = ref(false)
const downloadFile = () => {
  downloading.value = true
  const params = {
    travelSumReportId: id,
  }
  travelSummaryApi.exportSumReportDetail(params).then(res => {
    downloading.value = false
  }).catch(err => {
    downloading.value = false
  })
}
const columns: ColumnType[] = [
  {
    title: '员工姓名',
    dataIndex: 'nickName',
    align: 'center',
    ellipsis: true,
    width: 120,
    fixed: 'left',
  },

  {
    title: '员工工号',
    dataIndex: 'username',
    align: 'center',
    ellipsis: true,
    width: 120,
    fixed: 'left',
  },
  {
    title: '出差习惯',
    dataIndex: 'travelHabit',
    align: 'center',
    width: '200px',
  },

  {
    title: '提前4天申请的比例',
    width: '200px',
    dataIndex: 'advanceRate',
    align: 'center',
    ellipsis: true,
  },

  {
    title: '总节省',
    dataIndex: 'userSavedAmountAll',
    align: 'center',
    width: '100px',
    ellipsis: true,
  },

  {
    title: '合住节省',
    dataIndex: 'userSavedAmountHotel',
    align: 'center',
    width: '100px',
    ellipsis: true,
  },

  {
    title: '机票提前4天预订节省',
    dataIndex: 'userSavedAmountPlane',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },

  {
    title: '未节省',
    dataIndex: 'userSavingAmountAll',
    align: 'center',
    width: '100px',
    ellipsis: true,
  },

  {
    title: '未提前预订机票',
    dataIndex: 'userSavingAmountPlaneAdvance',
    align: 'center',
    width: '160px',
    ellipsis: true,
  },

  {
    title: '机票退改签',
    dataIndex: 'userSavingAmountHotelModify',
    align: 'center',
    width: '100px',
    ellipsis: true,
  },
  
];
// 
onMounted(async() => {
  currentRouter.value = await router

});

const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(travelSummaryApi.travelSumReportDetailPage);

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    travelSumReportId: id,
    readFlag: readFlag.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const dataSource = computed(() => {
  return data.value?.records || []
});


watch(
  () => id,
  (val: string) => {
    listApiRun({travelSumReportId: val, readFlag: readFlag.value ,pageNum:1,pageSize:10});
  },
  {
    immediate: true,
  },
)
// .
watch(
  () => readFlag.value,
  (val: string) => {
    listApiRun({travelSumReportId: id, readFlag: val ,pageNum:1,pageSize:10});
  },
)
</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 60px 60px 0px 60px;overflow: auto;">
    <h-row justify="space-between" style="margin-bottom: 20px;">
      <h-col style="font-size: 18px;">差旅报告明细</h-col>
      <h-col><h-button type="link" @click="currentRouter.back(-1)">返回</h-button></h-col>
    </h-row>

    <div>
      <h-descriptions  style="width: 800px; " :column="2" >
          <h-descriptions-item label="生成时间">{{ gmtCreate }}</h-descriptions-item>
          <h-descriptions-item label="报告人数">
            <div >{{ `${totalCount}人` }}</div>
          </h-descriptions-item>
        </h-descriptions>
        
      <h-row :align="'middle'" style="width: 100%; padding: 10px 10px 0px 10px;  margin-bottom: 20px;">
          <h-col :span="12" style="text-align: left;">
            <!-- <h-radio-group v-model:value="readFlag" style="margin: 8px">
              <h-radio-button value="">全部</h-radio-button>
              <h-radio-button value="1">已读</h-radio-button>
              <h-radio-button value="0">未读</h-radio-button>
            </h-radio-group> -->
          </h-col>
          <h-col :span="12" style="text-align: right;">
            <h-button  :loading="downloading" @click="downloadFile">导出</h-button>
          </h-col>
        </h-row>

        <h-table style="width:100%;" :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">

            <template v-if="column.dataIndex === 'readFlag'">
                {{ record.readFlag == '0' ? '未读' : '已读' }}            
            </template>

          </template>
        </h-table>
    </div>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
