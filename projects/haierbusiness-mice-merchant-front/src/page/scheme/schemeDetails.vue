<script setup lang="ts">
// 方案详情
import { onMounted, ref, onUnmounted, h, inject } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  resolveParam,
  routerParam,
  getDealTime,
  errorModal,
  delData,
  meetingProcessOrchestration,
} from '@haierbusiness-front/utils';
import advisors from '@haierbusiness-front/components/mice/advisors/index.vue';
import { message, Modal } from 'ant-design-vue';
import { schemeApi, miceBidManOrderListApi } from '@haierbusiness-front/apis';
import { HotelsArr, hotelLevelConstant, hotelLevelAllConstant } from '@haierbusiness-front/common-libs';

import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
const { loginUser } = storeToRefs(applicationStore());

const isCloseLastTab = inject<Ref<boolean>>('isCloseLastTab');

const route = useRoute();
const router = useRouter();
const previewSource = ref<string>('');

const miceId = ref<number>('');
const interactEndDate = ref<string>(''); // 方案提报截止时间
const countdownTime = ref<string>(''); // 方案提报截止时间
const timer = ref(null);

const pdMainId = ref<number>(null);
const pdVerId = ref<number>(null);

// 查看-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
const schemeType = ref<string>(''); // 方案提报类型

const lockLoading = ref<boolean>(false); //
const hotelLoading = ref<boolean>(false); // 抢单互动
const schemeLoading = ref<boolean>(false); // 抢单互动
const grabOrdersShow = ref<boolean>(false); // 抢单互动弹窗
const demandHotels = ref<array>([]); // 需求酒店
const hotelList = ref<array>([]); // 抢单互动酒店
const planList = ref<array>([]); // 我的方案
const selectedRowKeys = ref<array>([]); // 已选择
const selectedRows = ref<array>([]); // 已选择
const disabledHotels = ref<array>([]); // 不可选酒店
const selectedHotelCodes = ref<array>([]); // 已选择酒店codes
const tabsActiveKey = ref(0);

const abandonShow = ref<boolean>(false); // 取消锁定
const cancelRemark = ref<string>(''); // 取消锁定
const miceDemandHotelLockId = ref<string>(''); // 锁定Id

const searchHotelName = ref<string>('');
const searchHotelLevel = ref<number>(null);
const searchInput = ref();
const searchSelect = ref();

const merchantId = ref<number>(null); // 服务商id
const merchantType = ref<number>(null); // 服务商类型

const lockedHotelHour = ref<string>(''); // 锁定酒店后提报方案截止时间配置(单位: 小时)

const hotelPagination = ref({
  total: 0,
  current: 1,
  pageSize: 10,
});
const planPagination = ref({
  total: 0,
  current: 1,
  pageSize: 10,
});

// 符合条件酒店
let hotelColumns = [
  {
    title: '序号',
    dataIndex: '1',
    width: 50,
    ellipsis: true,
    align: 'center',
    customRender: ({ index }) => {
      return index + 1;
    },
  },
  {
    title: '酒店名称',
    dataIndex: 'dataIndex2',
    width: 280,
    ellipsis: true,
    align: 'center',
    customFilterDropdown: true,
    onFilterDropdownOpenChange: (visible) => {
      if (visible) {
        setTimeout(() => {
          searchInput.value.focus();
        }, 10);
      }
    },
    customRender: ({ record }) => {
      return record.hotelCode
        ? h(
            'a',
            {
              style: {
                color: '#1868DB',
              },
              onClick: () => {
                // 跳转 - 酒店详情
                const businessHotel = import.meta.env.VITE_BUSINESS_HOTEL + '/#';
                const url = businessHotel + '/hotel-analysis/hotelInfo?code=' + record.hotelCode + '&date';

                window.open(url);
              },
            },
            record.hotelName,
          )
        : '-';
    },
  },
  {
    title: '星级',
    dataIndex: 'dataIndex3',
    width: 80,
    ellipsis: true,
    align: 'center',
    customFilterDropdown: true,
    onFilterDropdownOpenChange: (visible) => {
      if (visible) {
        setTimeout(() => {
          searchSelect.value.focus();
        }, 10);
      }
    },
    customRender: ({ record }) => {
      return hotelLevelAllConstant.ofType(record.level)?.desc || '-';
    },
  },
  {
    title: '距离',
    dataIndex: '5',
    width: 100,
    align: 'center',
    ellipsis: true,
    customRender: ({ record }) => {
      return record.distance ? record.distance + 'km' : '-';
    },
  },
  {
    title: '操作',
    dataIndex: '6',
    width: 60,
    align: 'center',
    ellipsis: true,
    customRender: ({ record }) => {
      return demandHotels.value.length > 1
        ? '-'
        : h(
            'div',
            {
              style: {
                display: 'flex',
                justifyContent: 'center',
              },
            },
            [
              record.lockState === 10
                ? h('span', '已锁定')
                : record.lockState === 20
                ? h('span', '已提报')
                : record.lockState === 30
                ? h('span', '已过期')
                : h(
                    'a',
                    {
                      style: {
                        color: '#1868DB',
                      },
                      onClick: () => {
                        lockScheme(record.id ? record.id + '' : '', record.distance);
                      },
                    },
                    '锁定方案',
                  ),
            ],
          );
    },
  },
];
// 我的方案
const planColumns = [
  {
    title: '序号',
    dataIndex: '1',
    width: 50,
    ellipsis: true,
    align: 'center',
    customRender: ({ index }) => {
      return index + 1;
    },
  },
  {
    title: '酒店名称',
    dataIndex: '2',
    width: 280,
    ellipsis: true,
    align: 'center',
    customRender: ({ record }) => {
      const codes = record.platformHotelCodes || '';
      const codeList = codes ? codes.split(',') : [];

      const names = record.platformHotelNames || '';
      const nameList = names ? names.split(',') : [];

      let hotelNameList = [];
      nameList.forEach((e, idx) => {
        hotelNameList.push(
          h(
            'a',
            {
              style: {
                color: '#1868DB',
                display: 'block',
                lineHeight: '32px',
              },
              onClick: () => {
                // 跳转 - 酒店详情
                const businessHotel = import.meta.env.VITE_BUSINESS_HOTEL + '/#';
                const url = businessHotel + '/hotel-analysis/hotelInfo?code=' + codeList[idx] + '&date';

                window.open(url);
              },
            },
            e,
          ),
        );
      });

      return codes
        ? h(
            'div',
            {
              style: {},
            },
            [...hotelNameList],
          )
        : '-';
    },
  },
  {
    title: '距离',
    dataIndex: '4',
    width: 100,
    align: 'center',
    ellipsis: true,
    customRender: ({ record }) => {
      return record.distance ? record.distance + 'km' : '-';
    },
  },
  {
    title: '操作',
    dataIndex: '5',
    width: 120,
    align: 'center',
    ellipsis: true,
    customRender: ({ record }) => {
      // 已锁定 - 10
      // 已提报 - 20
      // 到期取消 - 30
      // 手动取消 - 40

      if (schemeType.value === 'schemeView') {
        // 查看方案
        return h(
          'div',
          {
            style: {
              display: 'flex',
              justifyContent: 'center',
            },
          },
          [
            h('span', '已提报'),
            h(
              'a',
              {
                style: {
                  color: '#1868DB',
                  marginLeft: '10px',
                },
                onClick: () => {
                  goSchemePath(record.id, '1');
                },
              },
              '查看',
            ),
          ],
        );
      }

      return record.state === 10
        ? h(
            'div',
            {
              style: {
                display: 'flex',
                justifyContent: 'center',
              },
            },
            [
              h(
                'a',
                {
                  style: {
                    color: '#1868DB',
                    marginRight: '10px',
                  },
                  onClick: async () => {
                    goSchemePath(record.id, '0');
                  },
                },
                '方案提报',
              ),
              h(
                'a',
                {
                  style: {
                    color: '#1868DB',
                  },
                  onClick: () => {
                    cancelRemark.value = '';
                    miceDemandHotelLockId.value = record.id;

                    abandonShow.value = true;
                  },
                },
                '取消锁定',
              ),
            ],
          )
        : record.state === 20 && (schemeType.value === 'notReported' || schemeType.value === 'reported')
        ? h(
            'a',
            {
              style: {
                color: '#1868DB',
                marginRight: '10px',
              },
              onClick: async () => {
                goSchemePath(record.id, '1');
              },
            },
            '方案调整',
          )
        : record.state === 20
        ? h(
            'div',
            {
              style: {
                display: 'flex',
                justifyContent: 'center',
              },
            },
            [
              h('span', '已提报'),
              h(
                'a',
                {
                  style: {
                    color: '#1868DB',
                    marginLeft: '10px',
                  },
                  onClick: () => {
                    goSchemePath(record.id, '1');
                  },
                },
                '查看',
              ),
            ],
          )
        : record.state === 30
        ? h('span', '已过期')
        : h(
            'a',
            {
              style: {
                color: '#1868DB',
              },
              onClick: () => {
                lockScheme(record.miceDemandPushHotelIds, record.distance);
              },
            },
            '锁定方案',
          );
    },
  },
];

// 方案互动页面
const goSchemePath = (id: number, type: string) => {
  let params = {
    miceId: miceId.value,
    interactEndDate: interactEndDate.value,
    schemeType: schemeType.value,
    pdMainId: pdMainId.value, //
    pdVerId: pdVerId.value, //
  };
  if (type === '0') {
    params.hotelLockId = id;
  } else {
    params.miceSchemeDemandHotelLockId = id;
  }

  if (isCloseLastTab) {
    // 关闭当前页签
    isCloseLastTab.value = true;
  }

  let url = '';

  // 查看-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
  if (schemeType.value === 'view' || schemeType.value === 'schemeView') {
    // 查看方案详情
    url = '/mice-merchant/scheme/schemeDetail';
  } else if (schemeType.value === 'notBidding') {
    // 竞价
    url = '/mice-merchant/scheme/schemeBinding';
  } else if (schemeType.value === 'biddingView') {
    // 查看竞价详情
    url = '/mice-merchant/scheme/schemeBiddingDetail';
  } else {
    // 方案互动
    url = '/mice-merchant/scheme/schemeInteract';
  }

  router.push({
    path: url,
    query: {
      record: routerParam(params),
    },
  });
};

// 锁定方案
const lockScheme = async (ids: string, distance: number) => {
  const isNotSub = planList.value.filter((e) => e.state === 10) || [];

  if (isNotSub.length > 0) {
    Modal.warning({
      title: '存在已锁定的方案，请先提报已锁定的方案',
      content: '',
      okText: '确定',
    });

    return;
  }

  let params = {
    miceId: miceId.value,
    miceDemandPushHotelIds: ids,
  };

  if (distance) {
    params.distance = distance;
  }

  if (lockLoading.value) {
    return;
  }

  lockLoading.value = true;

  const res = await schemeApi.lockHotel({ ...params }, (error) => {
    lockLoading.value = false;
    errorModal(error?.message);
  });

  lockLoading.value = false;

  if (res === null || res?.data === null) {
    Modal.success({
      title:
        '方案已锁定成功，请于' +
        lockedHotelHour.value +
        '小时内完成需求的提报，否则将释放此方案由其他服务商进行提报并产生扣分！',
      content: '',
      okText: '我已知晓',
    });

    selectedRowKeys.value = [];
    selectedRows.value = [];
    selectedHotelCodes.value = [];
    disabledHotels.value = [];
    await getHotelList(miceId.value);
    await getPlanList(miceId.value);

    // 锁定方案，删除缓存
    delCache();
  }
};

// 生成方案
const cratePlan = () => {
  if (selectedRowKeys.value.length === 0) {
    message.error('请选择酒店！');
    return;
  }
  if (selectedRowKeys.value.length < demandHotels.value.length) {
    message.error('酒店数量与酒店需求数量不一致，请继续选择酒店');
    return;
  }
  const ids = selectedRowKeys.value.join(',');

  lockScheme(ids, null);
};

// 方案提报
const schemeSub = async (type: string) => {
  searchHotelName.value = '';
  searchHotelLevel.value = null;

  if (merchantType.value === 2) {
    // 旅行社
    if (demandHotels.value && demandHotels.value.length === 0) {
      // 无酒店需求，直接跳转至方案互动页面
      goSchemePath('', '1');
      return;
    }

    if (type === 'schemePlan') {
      // 旅行社酒店列表
      hotelPagination.value.current = 1;
      hotelPagination.value.pageSize = 10;
      await getHotelList(miceId.value);
    }

    // 旅行社锁定方案
    planPagination.value.current = 1;
    planPagination.value.pageSize = 10;

    await getPlanList(miceId.value);

    if (hotelList.value.length === 0 && schemeType.value !== 'schemeView') {
      Modal.warning({
        title: '当前会议无匹配酒店！',
        content: '',
        okText: '确定',
      });

      return;
    }

    grabOrdersShow.value = true;
  } else if (merchantType.value === 1) {
    // 直签酒店
    // 旅行社锁定方案
    planPagination.value.current = 1;
    planPagination.value.pageSize = 10;
    await getPlanList(miceId.value);

    if (planList.value.length > 0) {
      const lockHotelId = planList.value[0].id;

      goSchemePath(type === 'schemePlan' ? lockHotelId : null, '0');
    } else {
      message.error('当前会议无匹配酒店！');
      return;
    }
  } else {
    // 其他服务商类型，直接跳转至方案互动页面
    goSchemePath('', '1');
  }
};

const closeModel = () => {
  grabOrdersShow.value = false;
};
// 取消锁定
const handleAbandon = async () => {
  if (!cancelRemark.value) {
    message.error('请填写取消原因！');
    return;
  }

  let params = {
    miceDemandHotelLockId: miceDemandHotelLockId.value,
    cancelRemark: cancelRemark.value,
  };

  const res = await schemeApi.lockHotelCancel({ ...params });

  if (res === null) {
    message.success('方案已放弃！');

    await getHotelList(miceId.value);
    await getPlanList(miceId.value);

    // 取消锁定，删除缓存
    delCache();
  }

  abandonShow.value = false;
};

const handleTableChange1 = (pagination) => {
  hotelPagination.value = {
    ...hotelPagination.value,
    current: pagination.current,
    pageSize: pagination.pageSize,
  };
  getHotelList(miceId.value);
};

const handleTableChange2 = (pagination) => {
  planPagination.value = {
    ...planPagination.value,
    current: pagination.current,
    pageSize: pagination.pageSize,
  };
  getPlanList(miceId.value);
};

// table多选框
const onSelectChange = (record: string[], selected: boolean) => {
  // 点击行勾选， 用于维护我勾选的所有数据
  if (selected) {
    selectedRowKeys.value.push(record.id);
    selectedRows.value.push(record);

    // 设置相同酒店需求的不可选
    if (!disabledHotels.value.includes(record.miceDemandHotelId)) {
      disabledHotels.value.push(record.miceDemandHotelId);
    }
    // 设置相同酒店不可选
    if (!selectedHotelCodes.value.includes(record.hotelCode)) {
      selectedHotelCodes.value.push(record.hotelCode);
    }
  } else {
    selectedRowKeys.value.splice(selectedRowKeys.value.indexOf(record.id), 1);
    selectedRows.value.splice(selectedRowKeys.value.indexOf(record.id), 1);

    // 设置相同酒店需求的不可选
    if (disabledHotels.value.includes(record.miceDemandHotelId)) {
      disabledHotels.value.splice(disabledHotels.value.indexOf(record.miceDemandHotelId), 1);
    }
    // 设置相同酒店不可选
    if (selectedHotelCodes.value.includes(record.hotelCode)) {
      selectedHotelCodes.value.splice(selectedHotelCodes.value.indexOf(record.hotelCode), 1);
    }
  }

  if (selectedRowKeys.value.length === demandHotels.value.length) {
    const checkedList = selectedRows.value.filter(
      (e) => selectedRowKeys.value.includes(e.id) && (e.lockState === 10 || e.lockState === 20),
    );

    if (checkedList.length === demandHotels.value.length) {
      const nameArr = checkedList.map((e) => {
        return e.hotelName;
      });
      const nameStr = nameArr.join('，');

      Modal.warning({
        title: '提示',
        content: '当前酒店:' + nameStr + '，已被其他服务商提报，若继续提报，会降低中标概率，请知悉！',
        okText: '我知道了',
      });
    }
  }
};

const chargeDetailCheckBox = (record) => {
  // 酒店选择数量 = 住宿需求数量 - 不可选
  // 排除已选择的
  // 来自相同酒店需求的 - 不可选
  return {
    disabled:
      (selectedRowKeys.value.length === demandHotels.value.length ||
        disabledHotels.value.includes(record.miceDemandHotelId) ||
        selectedHotelCodes.value.includes(record.hotelCode)) &&
      !selectedRowKeys.value.includes(record.id),
    name: record.hotelName,
  };
};

// 查询符合条件酒店
const getHotelList = async (miceId) => {
  if (!miceId) {
    message.error('查询失败！');
    return;
  }

  hotelLoading.value = true;

  try {
    const res = await schemeApi.pushHotelsDetails({
      hotelName: searchHotelName.value,
      level: searchHotelLevel.value,
      miceId: miceId,
      miceDemandHotelId: tabsActiveKey.value,
      pageNum: hotelPagination.value.current,
      pageSize: hotelPagination.value.pageSize,
    });

    hotelList.value = res.records || [];
    hotelPagination.value = {
      ...hotelPagination.value,
      total: res.total || 0,
      current: res.pageNum || 1,
    };
  } catch (error) {
    message.error('查询失败！');
  } finally {
    hotelLoading.value = false;
  }
};

// 查询我的方案
const getPlanList = async (miceId) => {
  if (!miceId) {
    message.error('我的方案查询失败！');
    return;
  }

  schemeLoading.value = true;

  try {
    const res = await schemeApi.lockRecord({
      miceId: miceId,
      pageNum: planPagination.value.current,
      pageSize: planPagination.value.pageSize,
    });

    planList.value = res.records || [];
    planPagination.value = {
      ...planPagination.value,
      total: res.total || 0,
      current: res.pageNum || 1,
    };
  } catch (error) {
    message.error('查询失败！');
  } finally {
    schemeLoading.value = false;
  }
};

// 获取当前酒店需求列表
const hotelListEmit = (hotelParams: HotelsArr) => {
  // 需求酒店
  // demandHotels.value.length > 1,多酒店模式
  // demandHotels.value.length === 1,单酒店模式
  demandHotels.value = hotelParams || [];

  tabsActiveKey.value = demandHotels.value[0]?.id || null;

  if (demandHotels.value.length > 1) {
    // 多酒店模式，隐藏状态
    hotelColumns = hotelColumns.slice(0, 4);
  }
};

const handleSearch = (selectedKeys, confirm, dataIndex) => {
  if (dataIndex === 'dataIndex2') {
    searchHotelName.value = selectedKeys[0];
  }
  confirm();
};

const handleReset = (clearFilters, dataIndex) => {
  if (dataIndex === 'dataIndex2') {
    searchHotelName.value = '';
  }
  if (dataIndex === 'dataIndex3') {
    searchHotelLevel.value = null;
  }

  clearFilters({ confirm: true });
};

const changeTabs = async () => {
  searchHotelName.value = '';
  searchHotelLevel.value = null;

  hotelPagination.value.current = 1;
  hotelPagination.value.pageSize = 10;
  await getHotelList(miceId.value);
};

// 缓存删除
const delCache = async () => {
  if (!miceId.value) {
    return;
  }

  delData({
    applicationCode: 'haierbusiness-mice-merchant',
    cacheKey:
      'haierbusiness-mice-merchant_' +
      loginUser.value?.username +
      '_schemeInteractKey' +
      miceId.value +
      '_merchantId' +
      merchantId.value, // 方案互动
  });
};

const getUser = async () => {
  // 获取登录服务商的类型
  const res = await schemeApi.getMerchantByUser({});

  // 服务商的类型
  // 1-酒店,2-旅行社,3-保险,4-礼品,5-用车
  merchantType.value = res.merchantType;
  merchantId.value = res.id;
};

// 流程详情
const getProcessDetails = async (processId = localStorage.getItem('processId') || '', verId = '') => {
  // 流程ID
  if (!processId) {
    message.error('流程ID不存在！');
    return;
  }

  const res = await miceBidManOrderListApi.processDetails({
    id: processId,
    verId: verId,
  });

  // 需求配置
  // 锁定酒店后提报方案截止时间配置(单位: 小时)
  lockedHotelHour.value = meetingProcessOrchestration(
    'SCHEME_SUBMIT',
    res.nodes || [],
    'schemeSubmitLockReleaseHourConfigDefine',
  );
};

onMounted(async () => {
  const record = resolveParam(route.query.record);
  miceId.value = record.miceId;
  interactEndDate.value = record.interactEndDate;
  schemeType.value = record.schemeType;
  pdMainId.value = record.pdMainId || null;
  pdVerId.value = record.pdVerId || null;

  // 流程详情
  await getProcessDetails(pdMainId.value, pdVerId.value);

  await getUser();

  // 方案提报截止时间
  timer.value = setInterval(() => {
    countdownTime.value = getDealTime(interactEndDate.value);
  }, 1000);

  previewSource.value = 'demandOne'; // 展示一个、详情接口
});

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
});
</script>

<template>
  <!-- 方案详情 -->
  <div class="wid1280 scheme_details">
    <advisors
      v-if="previewSource"
      :preview-source="previewSource"
      :isManagePage="true"
      :platformType="'merchant'"
      :merchantType="merchantType"
      @hotelListEmit="hotelListEmit"
    >
      <template #header> </template>
      <template #footer>
        <slot name="footer"></slot>
        <div v-if="schemeType !== 'view'">
          <!-- 非预览页面 -->
          <div class="pr16 scheme_btns" v-if="schemeType === 'schemeView'">
            <!-- 我的方案 -->
            <a-button type="primary" :loading="hotelLoading || schemeLoading" @click="schemeSub('myPlan')"
              >我的方案</a-button
            >
          </div>
          <div class="pr16 scheme_btns" v-else>
            <!-- 方案互动 -->
            <div v-show="countdownTime" class="mr24">
              <span>截止方案提报还剩</span>
              <span class="color_red mr2 ml2">
                {{ countdownTime }}
              </span>
              <span>，请尽快提报方案</span>
            </div>
            <a-button type="primary" :loading="hotelLoading || schemeLoading" @click="schemeSub('schemePlan')"
              >立即提报</a-button
            >
          </div>
        </div>
      </template>
    </advisors>

    <!-- 抢单互动 - 弹窗 -->
    <a-modal
      v-if="grabOrdersShow"
      v-model:open="grabOrdersShow"
      :title="
        schemeType !== 'schemeView'
          ? '抢单互动-' + (demandHotels.length > 1 ? '多' : '单') + '酒店模式符合条件酒店'
          : ''
      "
      width="1000px"
      centered
    >
      <div v-if="schemeType !== 'schemeView'">
        <div class="order_interaction_title">
          <div v-if="demandHotels.length > 1">
            <a-tabs v-model:activeKey="tabsActiveKey" @change="changeTabs">
              <a-tab-pane v-for="item in demandHotels" :key="item.id" :tab="item.centerMarker"> </a-tab-pane>
            </a-tabs>
          </div>
          <a-button v-if="demandHotels.length > 1" type="primary" :loading="lockLoading" @click="cratePlan()"
            >生成方案</a-button
          >
        </div>

        <a-table
          :columns="hotelColumns"
          :data-source="hotelList"
          :row-key="(record: { id: string; }) => record.id"
          :row-selection="
            demandHotels.length > 1
              ? {
                  selectedRowKeys: selectedRowKeys,
                  onSelect: onSelectChange,
                  hideSelectAll: true,
                  getCheckboxProps: chargeDetailCheckBox,
                }
              : null
          "
          @change="handleTableChange1"
          bordered
          size="small"
          :pagination="hotelPagination"
        >
          <template #customFilterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">
            <div style="padding: 8px">
              <div class="mb8">
                <a-input
                  v-if="column.dataIndex === 'dataIndex2'"
                  ref="searchInput"
                  placeholder="请输入酒店名称"
                  :value="selectedKeys[0]"
                  style="width: 240px"
                  @change="(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])"
                  @pressEnter="handleSearch(selectedKeys, confirm, column.dataIndex)"
                />

                <a-select
                  v-if="column.dataIndex === 'dataIndex3'"
                  ref="searchSelect"
                  v-model:value="searchHotelLevel"
                  style="width: 240px"
                  placeholder="请选择酒店星级"
                  @change="(e) => setSelectedKeys([searchHotelLevel])"
                  allow-clear
                >
                  <a-select-option v-for="item in hotelLevelConstant.toArray()" :key="item.code" :value="item.code">
                    {{ item.desc }}
                  </a-select-option>
                </a-select>
              </div>
              <div style="text-align: right">
                <a-button
                  type="primary"
                  class="mr8"
                  size="small"
                  @click="handleSearch(selectedKeys, confirm, column.dataIndex)"
                >
                  搜索
                </a-button>
                <a-button size="small" @click="handleReset(clearFilters, column.dataIndex)">重置</a-button>
              </div>
            </div>
          </template>
        </a-table>
      </div>

      <div class="">
        <a-divider orientation="left" :orientationMargin="0">我的方案</a-divider>
        <a-table
          :columns="planColumns"
          :data-source="planList"
          @change="handleTableChange2"
          bordered
          size="small"
          :pagination="planPagination"
        >
        </a-table>
      </div>

      <template #footer>
        <a-button @click="closeModel()">关闭</a-button>
      </template>
    </a-modal>

    <!-- 取消锁定 - 弹窗 -->
    <a-modal v-model:open="abandonShow" title="取消锁定" width="600px" :maskClosable="false" @ok="handleAbandon">
      <a-textarea
        style="margin: 24px 0 36px"
        v-model:value="cancelRemark"
        placeholder="取消原因"
        :maxlength="500"
        showCount
        allow-clear
      />
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.scheme_details {
  .scheme_btns {
    display: flex;
    justify-content: right;
    align-items: center;
  }

  .color_red {
    color: #ff5533;
  }

  :deep(.demand_contrast_footer) {
    z-index: 10;
  }
}
</style>
<style>
.order_interaction_title {
  margin-bottom: 10px;

  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
