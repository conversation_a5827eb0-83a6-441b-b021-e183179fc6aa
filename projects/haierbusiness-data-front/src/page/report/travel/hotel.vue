<template>
  <div style="
      background-color: #ffff;
      height: 100%;
      width: 100%;
      padding: 10px 10px 0px 10px;
      overflow: auto;
    ">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="sqr">创建人：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="sqr" v-model:value="searchKey.sqr" placeholder="" autocomplete="off" allow-clear/>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="ddbh">订单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="ddbh" v-model:value="searchKey.ddbh" placeholder="" autocomplete="off" allow-clear/>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="dd_ydsj">预订时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="searchKey.dd_ydsj" value-format="YYYY-MM-DD" style="width: 100%"/>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="rzcs_filter">入住城市：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="ddbh" v-model:value="searchKey.rzcs_filter" placeholder="" autocomplete="off" allow-clear/>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="jdmc_filter">酒店名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="jdmc_filter" v-model:value="searchKey.jdmc_filter" placeholder="" autocomplete="off"
                     allow-clear/>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="account_company_code">结算单位：</label>
          </h-col>
          <h-col :span="4">
            <h-select placeholder="请选择结算单位" v-model:value="searchKey.account_company_code" show-search
                      :filter-option="filterOption" allow-clear @search="handleSearch" :options="settleCompany"
                      style="width: 100%" :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="budget_department_code ">预算部门：</label>
          </h-col>
          <h-col :span="4">
            <h-select placeholder="请选择预算部门" v-model:value="searchKey.budget_department_code" show-search
                      allow-clear
                      :filter-option="filterOption" @search="handleBudgetSearch" :options="settleDepartment"
                      style="width: 100%"
                      :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="budget_source">预算类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select placeholder="请选择预算类型" v-model:value="searchKey.budget_source" :options="budgetTypeList"
                      allow-clear
                      style="width: 100%" :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="ddlxmc">订单类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select placeholder="请选择订单类型" v-model:value="searchKey.ddlxmc" :options="orderTypeOptions"
                      allow-clear
                      style="width: 100%" :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="field_code ">领域：</label>
          </h-col>
          <h-col :span="4">
            <h-select placeholder="请选择领域" v-model:value="searchKey.field_code" show-search allow-clear
                      :filter-option="filterOption" @search="handleAreaSearch" :options="areaList" style="width: 100%"
                      :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="pt_code">平台：</label>
          </h-col>
          <h-col :span="4">
            <h-select placeholder="请选择平台" v-model:value="searchKey.pt_code" show-search
                      :filter-option="filterOption" allow-clear
                      @search="handlePtSearch" :options="platformList" style="width: 100%"
                      :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="pl_code">产业线：</label>
          </h-col>
          <h-col :span="4">
            <h-select placeholder="请选择产业线" v-model:value="searchKey.pl_code" show-search
                      :filter-option="filterOption" allow-clear
                      @search="handlePlSearch" :options="industryList" style="width: 100%"
                      :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>

        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="pl_code">内外部数据：</label>
          </h-col>
          <h-col :span="4">
            <h-select v-model:value="searchKey.sjly_inner" show-search allow-clear
                      :options="sjlyInners" style="width: 100%"
                      :field-names="{ label: 'name', value: 'code' }">
            </h-select>
          </h-col>
        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <h-button style="margin-right: 10px" @click="handleReset">重置</h-button>
            <h-button style="margin-right: 10px" type="primary" @click="onFilterChange">
              <SearchOutlined/>
              查询
            </h-button>
            <h-button type="primary" style="margin-right: 10px" v-if="!pagination.disabled" :loading="downloading"
                      @click="download">
              <UploadOutlined/>
              导出
            </h-button>
          </h-col>
        </h-row>

        <!-- <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: left;">
            <h-button type="primary" @click="handleCreate">
              <PlusOutlined /> 新增员工
            </h-button>
          </h-col>
        </h-row> -->
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="(record) => record.id" :size="'small'" :data-source="data"
                 :pagination="pagination" :scroll="{ y: 550, x: 6000 }" :loading="loading" @change="onPageChange">
          <template #emptyText v-if="pagination.disabled">
            <div>暂无权限，<a @click="goApplyDetail">去申请</a></div>
          </template>
          <template #customFilterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">
            <!-- 日期选择器 -->
            <div v-if="column.title.indexOf('时间') != -1 || column.title.indexOf('日期') != -1" style="padding: 8px">
              <h-range-picker v-model:value="searchKey[column.key]" value-format="YYYY-MM-DD"
                              style="width: 218px; margin-bottom: 8px;"/>
              <div style="display: block">
                <a-button type="primary" size="small" style="width: 90px; margin-right: 8px" @click="onFilterChange">
                  <template #icon>
                    <SearchOutlined/>
                  </template>
                  搜索
                </a-button>
                <a-button size="small" style="width: 90px" @click="handleReset">
                  重置
                </a-button>
              </div>
            </div>
            <!-- 文本搜索框 -->
            <div v-else style="padding: 8px">
              <h-input ref="searchInput" :placeholder="`搜索${column.title}`"
                       v-model:value="searchKey[column.key.replace('_filter', '')]"
                       style="width: 188px; margin-bottom: 8px;"
                       allow-clear @pressEnter="onFilterChange"/>
              <div style="display: block">
                <a-button type="primary" size="small" style="width: 90px; margin-right: 8px" @click="onFilterChange">
                  <template #icon>
                    <SearchOutlined/>
                  </template>
                  搜索
                </a-button>
                <a-button size="small" style="width: 90px" @click="handleReset">
                  重置
                </a-button>
              </div>
            </div>
          </template>
          <template #customFilterIcon="{ filtered }">
            <SearchOutlined :style="{ color: filtered ? '#108ee9' : undefined }"/>
          </template>
        </h-table>
      </h-col>
    </h-row>
  </div>

  <!-- <div v-if="visible">
  <edit-dialog
        :show="visible"
        :data="editData"
        @cancel="onDialogClose"
        @ok="handleOk"
    >
    </edit-dialog> 
  </div> -->
</template>

<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Input as hInput,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  Table as hTable,
} from 'ant-design-vue';
import { onMounted, reactive, ref } from "vue";
import { SearchOutlined, UploadOutlined } from '@ant-design/icons-vue';

import { reset } from "@haierbusiness-front/utils/src/commonUtil";
import { checkUserGroups } from "@haierbusiness-front/utils/src/authorityUtil";
import { useSearch } from "../../../composables/useSearch";
import { ApplyCompanyType, ReportFilter, ReportType, UserGroupSystemConstant, } from "@haierbusiness-front/common-libs";
import { reportApi } from "@haierbusiness-front/apis";

import { aggregatorsToColumn, travelHotelColumns } from "../columns";

import { getCurrentRouter } from "@haierbusiness-front/utils";

const router = getCurrentRouter();

const columns = aggregatorsToColumn(
    checkUserGroups(
        [
          UserGroupSystemConstant.SUPER_MANAGE.groupId,
          UserGroupSystemConstant.REPORT_CONTROL.groupId,
        ],
        "OR"
    )
        ? travelHotelColumns
        : travelHotelColumns.filter(
            (item) =>
                item.alias != "供应商订单号" &&
                item.alias != "供应商" &&
                item.alias != "政策节省" &&
                item.alias != "服务费" &&
                item.alias != "实际入住日期" &&
                item.alias != "实际离店日期"
        )
);

const orderTypeOptions = [
  {
    name: "酒店正常单",
    code: "酒店正常单",
  },
  {
    name: "酒店退单",
    code: "酒店退单",
  },
];

const sjlyInners = reactive([{
  code: '1',
  name: '内部数据'
}]);

const searchKey = reactive<ReportFilter>({
  ddbh: null,
  sqr: null,
  sjly_inner: '1',
  dd_ydsj: [] as string[],
  account_company_code: null,
  datartParams: {
    moduleType: 1,
    type: "hotel",
    viewId: "0eeb8c921fb14520b396e5643e5bbe3f",
    aggregators: checkUserGroups(
        [
          UserGroupSystemConstant.SUPER_MANAGE.groupId,
          UserGroupSystemConstant.REPORT_CONTROL.groupId,
        ],
        "OR"
    )
        ? travelHotelColumns
        : travelHotelColumns.filter(
            (item) =>
                item.alias != "供应商订单号" &&
                item.alias != "供应商" &&
                item.alias != "政策节省" &&
                item.alias != "服务费" &&
                item.alias != "实际入住日期" &&
                item.alias != "实际离店日期"
        ),
    defaultFilters: [{
      aggOperator: null,
      column: ["sjly_inner"],
      sqlOperator: "EQ",
      values: [{
        value: "1",
        valueType: "STRING",
      }]
    }],
    orders: [{
      "column": [
        "dd_ydsj"
      ],
      "operator": "DESC"
    }],
    functionColumns: [
      {
        alias: "rzcs_filter",
        snippet: "if(filter_flag=1,'**',rzcs)",
      },
      {
        alias: "jdmc_filter",
        snippet: "if(filter_flag=1,'*******',jdmc)",
      },
      {
        alias: "jdxjmc_filter",
        snippet: "if(filter_flag=1,'***',jdxjmc)",
      },
      {
        alias: "jddz_filter",
        snippet: "if(filter_flag=1,'**********',jddz)",
      },
      {
        alias: "kflx_filter",
        snippet: "if(filter_flag=1,'*****',kflx)",
      },
      {
        alias: "ydsj_filter",
        snippet: "if(filter_flag=1,'*********',ydsj)",
      },
      {
        alias: "sjrzrq_filter",
        snippet: "if(filter_flag=1,'*********',sjrzrq)",
      },
      {
        alias: "sjldrq_filter",
        snippet: "if(filter_flag=1,'*********',sjldrq)",
      },
      {
        alias: "rzr_filter",
        snippet: "if(filter_flag=1,'*********',rzr)",
      },
    ],
  },
  fileName: "差旅酒店",
});
const goApplyDetail = () => {
  router.push("/data/report/permission/apply");
};

const handleReset = () => {
  searchKey.sjly_inner = '1';
  reset(searchKey, ["datartParams", 'fileName', 'sjly_inner']);
  onFilterChange();
};

const {
  data,
  fetchData,
  pagination,
  loading,
  onPageChange,
  onTimeChange,
  downloading,
  download,
  onFilterChange,
  powerDepartment,
  powerCompany,
} = useSearch<ReportType, ReportFilter>(reportApi, searchKey, "travel-hotel");

const settleCompany = ref([] as Array<ApplyCompanyType>);

const handleSearch = (val: string) => {
  //管理员搜索
  getPowerByApprove(val, 2);
};
const handleBudgetSearch = (val: string) => {
  //管理员搜索
  getPowerByApprove(val, 1);
};

// const handleAreaSearch = (val: string) => {
//   //管理员搜索
//   getAreaList({ type: "hotel", moduleType: 1, keyword: val });
// };

// const areaList = ref([]);
// const getAreaList = async (params) => {
//   //查询领域
//   const data = await reportApi.queryAreaList(params);
//   if (data && data.length > 0) {
//     areaList.value = data;
//   }
// };

const filterOption = (input: string, option: any) => {
  return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const settleDepartment = ref([]);
const querySettleDepartment = async (keyword: string) => {
  //查询部门
  const data = await reportApi.querySettleDepartment(keyword);
  if (data && data.length > 0) {
    settleDepartment.value = data;
  }
};

const budgetTypeList = ref([]);

// 查询领域、平台、产业线
const areaList = ref([]);
const platformList = ref([]);
const industryList = ref([]);

// 根据类型查询不同权限类型 permissionType 3:领域 4:平台 5:产业线
const getPowerByApprove = async (name: string, permissionType: number) => {
  const data = await reportApi.getPowerByApprove(name, permissionType, 1, 'hotel');
  switch (permissionType) {
    case 1:
      settleDepartment.value = data;
      break;
    case 2:
      settleCompany.value = data;
      break;
    case 3:
      areaList.value = data;
      break;
    case 4:
      platformList.value = data;
      break;
    case 5:
      industryList.value = data;
      break;
    case 6:
      budgetTypeList.value = data;
      break;
    case 99: {
      if (data && data.length > 0) {
        sjlyInners.splice(0, sjlyInners.length);
        data.forEach(item => {
          sjlyInners.push(item)
        })
      }

      let count = sjlyInners.filter(sjly => sjly.code == '2').length
      if (count > 0) {
        for (let i = 0; i < searchKey.datartParams.defaultFilters.length; i++) {
          if (searchKey.datartParams.defaultFilters[i].column[0] == 'sjly_inner') {
            searchKey.datartParams.defaultFilters.splice(i, 1)
          }
        }
      }
    }
      break;
    default:
      break;
  }
};

const handleAreaSearch = (val: string) => {
  getPowerByApprove(val, 3);
};

const handlePtSearch = (val: string) => {
  getPowerByApprove(val, 4);
};

const handlePlSearch = (val: string) => {
  getPowerByApprove(val, 5);
};

onMounted(() => {
  // getAreaList({ type: "hotel", moduleType: 1, keyword: "" })
  getPowerByApprove("", 1);
  getPowerByApprove("", 2);
  getPowerByApprove("", 3);
  getPowerByApprove("", 4);
  getPowerByApprove("", 5);
  getPowerByApprove("", 6);
  getPowerByApprove("", 99);
});
</script>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
