<template>
  <div
    v-loading="loading"
    background="rgba(0,0,0,0)"
    :id="id + props.iden"
    :style="{ height: '100%', width: '100%' }"
  ></div>
</template>
<script setup lang="ts">
import {onBeforeUnmount, onMounted, ref} from 'vue';
import * as echarts from 'echarts';
import {colorsforSmart} from '../../../page/board/data';
import {EventBus} from '../../../page/board/eventBus';
import {smartBrainApi} from '@haierbusiness-front/apis';
import {useRoute} from 'vue-router';

const route = useRoute();
const props = defineProps({
  height: {
    type: Number,
    default: 33,
  },
  id: {
    type: [String, Number],
    default: 'cicle-' + Date.now(),
  },
  echartsJson: {
    type: String,
    default: '',
  },
  // 图标类型
  dataType: {
    type: String,
    default: '',
  },
  searchForm: {
    type: Object,
    default: {},
  },
  iden: {
    type: String,
    default: '',
  },
});
const payTypeCheck = ref<string>('');
// const id = ref("cicle-" + Date.now());
const loading = ref(false);
let chartDom: any, myChart: any;
onMounted(() => {
  chartDom = document.getElementById(props.id + props.iden);
  window.addEventListener('resize', resize);
  setTimeout(() => {
    myChart = echarts.init(chartDom as any, 'dark');
    queryData();
  }, 300);
});
const setOption = JSON.parse(props.echartsJson);
const paramsData: any = ref({});
const unsubscribe = EventBus.on((event, params) => {
  if (event == 'refresh') {
    if (!params) queryData();
    if (params && params.from != 'date') {
      paramsData.value = params;
      queryData();
    }
  }
});
// 在组件销毁前移除事件监听器
onBeforeUnmount(() => {
  unsubscribe();
});
const queryData = async (params?: { data: { name: string }; from: string }) => {
  loading.value = true;
  const data = await smartBrainApi.queryCommonData({ brainReportIndicatorId: props.id, ...props.searchForm });
  loading.value = false;
  // 饼图处理方法
  if (setOption.dataType == '饼图') {
    const rows: any = [];
    data.rows.forEach((item: any) => {
      rows.push({
        value: item[1],
        name: item[0],
      });
    });
    const rowsIndexArray = JSON.parse(JSON.stringify(rows));
    rowsIndexArray.sort(function (a: any, b: any) {
      return b.value - a.value;
    });
    const { series, label, legend }: any = setOption;
    (legend.textStyle = {
      //图例文字的样式
      color: '#000',
    }),
      (label.fontSize = 12);
    label.color = '#000';
    // series[0].color = colorsforSmart;
    rows.forEach((item: any) => {
      const index = rowsIndexArray.indexOf(rowsIndexArray.filter((d: any) => d.name == item.name)[0]);
      if (index < 10) {
        item.itemStyle = {
          color: colorsforSmart[index], // 使用颜色映射表中对应的颜色
        };
      }
    });

    try {
      console.log(rows);
      let length = rows.filter((item: any) => item.name == "EES数据" || item.name == "商旅数据").length;
      console.log(length);
      if(length > 0){
        console.log("上平台率转换");
        if(rows[0].value > 100000000 && rows[1].value > 100000000){
          rows[0].value = (rows[0].value/100000000).toFixed(2);
          rows[0].name = rows[0].name + '（亿）'

          rows[1].value = (rows[1].value/100000000).toFixed(2);
          rows[1].name = rows[1].name + '（亿）'
        }else if(rows[0].value > 10000 && rows[1].value > 10000){
          rows[0].value = (rows[0].value/10000).toFixed(2);
          rows[0].name = rows[0].name + '（万）'

          rows[1].value = (rows[1].value/10000).toFixed(2);
          rows[1].name = rows[1].name + '（万）'
        }
      }
    }catch (e){
      console.log("饼图价格展示转换异常",e)
    }

    series[0].data = rows;
    (series[0].center = ['50%', '40%']), myChart.clear();
    console.log(setOption, '****');
    myChart.setOption({
      ...setOption,
    });
  } else if (setOption.dataType == '分布图') {
    const lineData1: any = [];
    const xData: any = [];
    data.rows.forEach((item, index) => {
      xData.push(item[0] + '时');
      lineData1.push(item[1] || 0);
    });
    const { series, xAxis }: any = setOption;
    xAxis.value = xData;
    series[0].data = lineData1;
    myChart.clear();
    myChart.setOption({
      ...setOption,
    });
  } else if (setOption.dataType == '柱状图') {
    // 柱状图处理方法
    if (!setOption.legend.data.includes('退票率')) {
      let barData: any = [];
      let lineData: any = [];
      let xData: any = [];
      data.rows.forEach((item: any, index: number) => {
        xData.push(item[0]);
        if (data.columns.length == 4 && data.columns[3].name[0] == '平均折扣') {
          lineData.push(item[3] || 0);
        } else {
          lineData.push(item[1] || 0);
        }
        if (item.length < 3) {
          barData.push(item[1] || 0);
          //  lineData.push(item[1] || 0);
        } else {
          barData.push(item[2] || 0);
          // lineData.push(item[3] || 0);
        }
      });
      const { series, xAxis, yAxis, legend }: any = setOption;
      legend.textStyle = {
        //图例文字的样式
        color: '#000',
      };
      let num = 0;
      series.forEach((item: any, index: number) => {
        if (item.type == 'bar') {
          num = num + 1;
          if (num == 1) {
            item.color = '#0073E5';
            if (item.itemStyle) {
              item.itemStyle.borderColor = '#0073E5';
            }
          }
        }
      });
      yAxis.forEach((item: any) => {
        if (item.name == '万元' || item.name == '数量(万)') {
          item.axisLabel = {
            formatter(value) {
              return value / 10000;
            },
            color: '#333941',
          };
        } else {
          item.axisLabel = {
            color: '#333941',
          };
        }
      });
      xAxis.forEach((item: any) => {
        item.axisLabel = {
          color: '#333941',
          rotate: 30,
        };
      });
      xAxis[0].data = xData;
      series[0].data = barData;
      if (series.length >= 2) {
        series[1].data = lineData;
      }
      myChart.setOption({
        ...setOption,
      });
    } else {
      const { series, xAxis, yAxis, legend }: any = setOption;
      legend.textStyle = {
        //图例文字的样式
        color: '#000',
      };
      let num = 0;
      series.forEach((item: any, index: number) => {
        if (item.type == 'bar') {
          num = num + 1;
          if (num == 1) {
            item.color = '#0073E5';
            if (item.itemStyle) {
              item.itemStyle.borderColor = '#0073E5';
            }
          }
        }
      });
      // data.columns.forEach((item, index) => {
      //     if ((index = 0)) return;
      //     legend.push(item.name[0]);
      // });
      const barData1: any = [];
      const barData2: any = [];
      const lineData1: any = [];
      const lineData2: any = [];
      const xData: any = [];
      data.rows.forEach((item, index) => {
        xData.push(item[0]);
        barData1.push(item[1] || 0);
        barData2.push(item[2] || 0);
        // barData1.push((item[1]||0)/10000);
        // barData2.push((item[2]||0)/10000);
        const item3 = (item[3] || 0) * 100;
        const item4 = (item[4] || 0) * 100;
        lineData1.push(item3.toFixed(2));
        lineData2.push(item4.toFixed(2));
      });
      console.log(xAxis,"////////////")
      xAxis[0].data = xData;
      yAxis.forEach((item: any) => {
        if (item.name == '万元') {
          item.axisLabel = {
            formatter(value) {
              return value / 10000;
            },
            color: '#333941',
          };
        } else {
          item.axisLabel = {
            color: '#333941',
          };
        }
      });
      xAxis.forEach((item: any) => {
        item.axisLabel = {
          color: '#333941',
          rotate: 30,
        };
      });
      console.log(9089899, barData1, series);
      series.forEach((item: any) => {
        if (item.name == '改签费') {
          if(props.id==56){
            item.data = barData1
          }else{
            item.data = barData2;
          }
        }
        if (item.name == '退票费') {
          if(props.id==56){
            item.data = barData2
          }else{
            item.data = barData1;
          }
        }
        if (item.name == '改签率') {
          item.data = lineData1;
        }
        if (item.name == '改期率') {
          item.data = lineData1;
        }
        if (item.name == '退票率') {
          item.data = lineData2;
        }
      });
      console.log(series);
    }
    myChart.setOption({
      ...setOption,
    });
  } else if (setOption.dataType == '多柱线图') {
    const {series, xAxis, yAxis, legend}: any = setOption;
    legend.textStyle = {
      //图例文字的样式
      color: '#000',
    };

    yAxis.forEach((item: any) => {
      if (item.name == '万元' || item.name == '数量(万)') {
        item.axisLabel = {
          formatter(value) {
            return value / 10000;
          },
          color: '#333941',
        };
      } else {
        item.axisLabel = {
          color: '#333941',
        };
      }
    });
    xAxis.forEach((item: any) => {
      item.axisLabel = {
        color: '#333941',
        rotate: 30,
      };
    });

    series.forEach((item: any, index: number) => {
      item.data.length = 0
      // xAxis[0].data.length = 0

      data.rows.forEach((dataRow: any[]) => {
        if (index == 0) {
          if (xAxis[0].data.length != data.rows.length) {
            xAxis[0].data.push(dataRow[0]);
          }
        }
        item.data.push(dataRow[index + 1] || 0)
      })
    })

    // 去掉全是 0 的数据
    for (let i = series.length - 1; i >= 0; i--) {
      if(isSameArray(series[i].data, 0)){
        series.splice(i, 1);
        legend.data.splice(i, 1);
      }
    }

    myChart.setOption({
      ...setOption,
    });
  }
  //   return rows;
};

const isSameArray = (arr:any[], checkItem:any) => {
  return arr.filter((item) => item != checkItem).length == 0;
}

const resize = () => {
  myChart.resize();
};
</script>
<style scoped lang="less">
:deep(.el-loading-mask) {
  /* 设置背景颜色为半透明的白色 */
  background-color: rgba(255, 255, 255, 0.5);
}
</style>
