import { loadEnv } from 'vite'

import vue from '@vitejs/plugin-vue'
import path from 'path';
import {writeFileSync} from "fs";

const CWD = process.cwd();

export default ({ mode }) => {
  const { VITE_BASE_URL } = loadEnv(mode, CWD);
  // 生成版本信息插件
  const generateVersionPlugin = {
    name: 'generate-version',
    // 仅在构建时执行
    apply: 'build',
    closeBundle() {
      // 生成版本文件
      const versionInfo = {
        // 使用时间戳作为版本号
        version: Date.now(),
        buildTime: new Date().toISOString(),
        mode: mode
      };

      writeFileSync(
          path.resolve(__dirname, 'dist/version.json'),
          JSON.stringify(versionInfo, null, 2)
      );

    }
  };
  return {
    base: VITE_BASE_URL,
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './'),
        '@': path.resolve(__dirname, './src'),
      },
    },
    plugins: [vue(),generateVersionPlugin],
    build: {
      target: ['es2015']
    },
    server: {
      port: 5181,
      proxy: {
        "/hb/ai/api": {
          // target: "http://localhost:8080/hb",
          //  target: "https://businessmanagement-test.haier.net/hbweb/payman/hb",
          target: "http://localhost:9219",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/hb\/ai\/api/, ""),
        },
        // "/hb/data/api": {
        //   target: "http://************:9210/",
        //   changeOrigin: true,
        //   rewrite: (path) => path.replace(/^\/hb\/data\/api/, ""),
        // },
        "/hb": {
          target: "https://businessmanagement-test.haier.net/hbweb/data/hb",
          // target: "http://localhost:8080/hb",
          // target: "http://************:9210/hbweb/data/hb",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/hb/, ""),
        },
        "/upload": {
          target: "https://businessmanagement-test.haier.net/hbweb/upload",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/upload/, ""),
        },
      }, 
    }
  }
}
