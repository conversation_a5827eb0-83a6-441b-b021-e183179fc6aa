import { createApp } from 'vue'
import Vant from 'vant';
import router from './router';
import './assets/css/main.less'
import App from './App.vue'
import { setGlobalOptions } from 'vue-request';
import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/reset.css';
import 'vant/lib/index.css';
import {autoCheckVersion} from "@haierbusiness-front/composables/src/checkVersion";

autoCheckVersion();
setGlobalOptions({
    manual: true,
    pagination: {
        currentKey: 'pageNum',
        pageSizeKey: 'pageSize',
    },
});

createApp(App)
    .use(Antd)
    .use(Vant)
    .use(router)
    .mount('#app')
