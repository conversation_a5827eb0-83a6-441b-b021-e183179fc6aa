import { createApp } from 'vue';
import router from './router';
import './assets/css/main.less';
import App from './App.vue';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
import { setGlobalOptions } from 'vue-request';
import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/reset.css';
import { CustomUpload, CommonBox } from './components';
import {autoCheckVersion} from "@haierbusiness-front/composables/src/checkVersion";

autoCheckVersion();
setGlobalOptions({
  manual: true,
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
  },
});

router.then((it) => {
  const app = createApp(App);

  app
    .use(globalPinia as any)
    .use(Antd)
    .use(it as any)
    .component('custom-upload', CustomUpload)
    .component('common-box', CommonBox)
    .mount('#app');

  app.config.globalProperties.$hbRouter = it;
});
