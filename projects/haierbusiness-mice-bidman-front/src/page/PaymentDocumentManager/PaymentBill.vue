<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  Upload as hUpload,
  Modal,
  message,
  TableProps,
  Table as ATable,
  Tabs as ATabs,
  TabPane as ATabPane,
  Textarea as ATextarea,
  DatePicker as hDatePicker,
  InputNumber as hInputNumber
} from 'ant-design-vue';
import {ColumnType} from 'ant-design-vue/lib/table/interface';
import {Key} from 'ant-design-vue/lib/vc-table/interface';
import {PlusOutlined, SearchOutlined, UploadOutlined} from '@ant-design/icons-vue';
import {paymentFromApi, fileApi} from '@haierbusiness-front/apis';
import {
  IPaymentFromFilter,
  IPaymentFrom
} from '@haierbusiness-front/common-libs';
import dayjs, {Dayjs} from 'dayjs';
import {computed, ref, watch, onMounted} from 'vue';
import {DataType, usePagination, useRequest} from 'vue-request';
import {useEditDialog, useDelete} from '@haierbusiness-front/composables'
import router from '../../router'
import {
  PaymentFromStatusEnum,
  PaymentFromStatusMap,
  PaymentFromStatusTagColorMap
} from '@haierbusiness-front/common-libs'
import Actions from '@haierbusiness-front/components/actions/Actions.vue'
import type {MenuItemType, MenuInfo} from 'ant-design-vue/lib/menu/src/interface'
import {flatMap} from 'lodash';
// const router = useRouter()

const currentRouter = ref()

onMounted(async () => {
  currentRouter.value = await router
  // 页面初始化时调用列表接口
  listApiRun({
    pageNum: 1,
    pageSize: 10
  })
})

const columns: ColumnType[] = [
  {
    title: '结算单号',
    dataIndex: 'receivePaymentCode',
    width: '250px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '服务商名称',
    dataIndex: 'merchantName',
    width: '320px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '账单总金额',
    dataIndex: 'totalAmount',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({text}) => text != null ? `${text}元` : '',
  },
  {
    title: '结算比例',
    dataIndex: 'settlementRate',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({text}) => text != null ? `${text}%` : '',
  },
  {
    title: '收款金额',
    dataIndex: 'receivePaymentAmount',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({text}) => text != null ? `${text}元` : '',
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '250px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<IPaymentFromFilter>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(paymentFromApi.getPaymentPage);

// 生成缴费单弹框的独立查询参数
const paymentOrderSearchParam = ref<{
  startTime?: string;
  endTime?: string;
  merchantCode?: string;
}>({})
const paymentOrderBeginAndEnd = ref<[Dayjs, Dayjs]>()


const reset = () => {
  searchParam.value = {}
  beginAndEnd.value = undefined
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total || 0,
  current: data.value?.pageNum || 1,
  pageSize: data.value?.pageSize || 10,
  style: {justifyContent: 'center'},
}));

const handleTableChange = (
    pag: { current: number; pageSize: number },
    filters?: any,
    sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};


// 删除
const {handleDelete} = useDelete({remove: paymentFromApi.removePayment}, () => listApiRun({
  ...searchParam.value,
  pageNum: data.value?.pageNum || 1,
  pageSize: data.value?.pageSize || 10,
}))

// View变量已删除，功能合并到viewMode中
const detailVisible = ref(false)
const currentDetailRecord = ref<any>(null)
const detailLoading = ref(false)
const viewMode = ref<'view' | 'upload'>('view') // 弹窗模式：查看或上传

// 上传发票相关
const invoiceModalVisible = ref(false)
const invoiceLoading = ref(false)
const currentInvoiceRecord = ref<any>(null)
const invoiceForm = ref({
  invoiceType: 1, // 默认选择国旅
  invoiceDate: '',
  invoiceNumber: '',
  invoiceAmount: undefined
})

//查看
const handleView = (record?: any) => {
  if (record && record.id) {
    // 有传入记录，获取详情数据
    detailLoading.value = true
    viewMode.value = 'view'
    paymentFromApi.getPaymentDetails(record.id).then((res) => {
      currentDetailRecord.value = res
      detailVisible.value = true
    }).catch((error) => {
      console.error('获取详情失败:', error)
      message.error('获取详情失败，请重试')
    }).finally(() => {
      detailLoading.value = false
    })
  } else {
    // 关闭详情弹窗
    detailVisible.value = false
    currentDetailRecord.value = null
    fileList.value = []
    ReasonsRejection.value = ''
  }
}

const beginAndEnd = ref<[Dayjs, Dayjs]>()
watch(() => beginAndEnd.value, (n: any, o: any) => {
  if (n) {
    searchParam.value.startTime = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
    searchParam.value.endTime = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
  } else {
    searchParam.value.startTime = undefined
    searchParam.value.endTime = undefined
  }
});

// 监听生成缴费单弹框的时间选择器变化
watch(() => paymentOrderBeginAndEnd.value, (n: any, o: any) => {
  if (n) {
    paymentOrderSearchParam.value.startTime = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
    paymentOrderSearchParam.value.endTime = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
  } else {
    paymentOrderSearchParam.value.startTime = undefined
    paymentOrderSearchParam.value.endTime = undefined
  }
});

// 上传付款凭证相关
const uploadLoading = ref(false)
const activeKey = ref('1');
const fileList = ref<any[]>([])
const ReasonsRejection = ref('')
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

// 打开上传付款凭证弹窗
const openUploadModal = (record: any) => {
  detailLoading.value = true
  viewMode.value = 'upload'
  // 获取详情数据
  paymentFromApi.getPaymentDetails(record.id).then((res) => {
    currentDetailRecord.value = res
    detailVisible.value = true
    fileList.value = []
    ReasonsRejection.value = ''
  }).catch((error) => {
    console.error('获取详情失败:', error)
    message.error('获取详情失败，请重试')
  }).finally(() => {
    detailLoading.value = false
  })
}

// 关闭上传弹窗（已合并到handleView函数中）

// 打开上传发票弹窗
const openInvoiceModal = (record: any) => {
  currentInvoiceRecord.value = record
  invoiceModalVisible.value = true
  // 重置表单
  invoiceForm.value = {
    invoiceType: 1,
    invoiceDate: '',
    invoiceNumber: '',
    invoiceAmount: undefined
  }
}

// 关闭上传发票弹窗
const closeInvoiceModal = () => {
  invoiceModalVisible.value = false
  currentInvoiceRecord.value = null
  invoiceForm.value = {
    invoiceType: 1,
    invoiceDate: '',
    invoiceNumber: '',
    invoiceAmount: undefined
  }
}

// 提交上传发票
const submitInvoice = () => {
  // 验证表单
  if (!invoiceForm.value.invoiceDate) {
    message.error('请选择发票日期')
    return
  }
  if (!invoiceForm.value.invoiceNumber) {
    message.error('请输入发票号')
    return
  }
  if (!invoiceForm.value.invoiceAmount || invoiceForm.value.invoiceAmount <= 0) {
    message.error('请输入有效的发票金额')
    return
  }

  if (!currentInvoiceRecord.value?.id) {
    message.error('记录ID不存在')
    return
  }

  invoiceLoading.value = true

  // 调用uploadInvoice接口
  paymentFromApi.uploadInvoice({
    paymentId: currentInvoiceRecord.value.id,
    paymentCode: currentInvoiceRecord.value.receivePaymentCode,
    invoiceType: invoiceForm.value.invoiceType,
    invoiceDate: invoiceForm.value.invoiceDate,
    invoiceNumber: invoiceForm.value.invoiceNumber,
    invoiceAmount: invoiceForm.value.invoiceAmount
  }).then(() => {
    message.success('发票上传成功')
    closeInvoiceModal()
    // 刷新列表
    listApiRun({
      ...searchParam.value,
      pageNum: data.value?.pageNum || 1,
      pageSize: data.value?.pageSize || 10,
    })
  }).catch((error) => {
    console.error('上传发票失败:', error)
    message.error('上传发票失败，请重试')
  }).finally(() => {
    invoiceLoading.value = false
  })
}

// 关闭生成付款单弹窗
const closePaymentOrderModal = () => {
  PaymentOrderVisible.value = false
  settlementList.value = undefined
  // 重置弹窗中的查询条件
  paymentOrderBeginAndEnd.value = undefined
  paymentOrderSearchParam.value = {}
}
//发票
const invoiceColumns: ColumnType<DataType>[] = [
  {
    title: '发票号',
    dataIndex: 'name',
  },
  {
    title: '发票日期',
    dataIndex: 'age',
  },
  {
    title: '发票金额',
    dataIndex: '',
    customRender: ({text}) => text != null ? `${text}元` : '',
  },
];

// 详情页订单表格列
const detailOrderColumns: ColumnType<DataType>[] = [
  {
    title: '会议单号',
    dataIndex: 'mainCode',
    width: '150px',
    align: 'center',
  },
  {
    title: '会议时间',
    width: '200px',
    align: 'center',
    customRender: ({record}) => {
      if (record.startDate && record.endDate) {
        return `${record.startDate} 至 ${record.endDate}`;
      }
      return '';
    },
  },
  {
    title: '会议负责人',
    dataIndex: 'operatorName',
    width: '120px',
    align: 'center',
  },
  {
    title: '账单金额',
    dataIndex: 'billAmount',
    width: '120px',
    align: 'center',
    customRender: ({text}) => text != null ? `${text}元` : '',
  },
  {
    title: '服务费率',
    dataIndex: 'feeRate',
    width: '100px',
    align: 'center',
    customRender: ({text}) => text != null ? `${text}%` : '',
  },
  {
    title: '付款金额',
    dataIndex: 'paymentAmount',
    width: '120px',
    align: 'center',
    customRender: ({text}) => text != null ? `${text}元` : '',
  },
];

// 详情页发票表格列
const detailInvoiceColumns: ColumnType<DataType>[] = [
  {
    title: '发票号',
    dataIndex: 'invoiceNumber',
    align: 'center',
  },
  {
    title: '发票日期',
    dataIndex: 'invoiceDate',
    align: 'center',
  },
  {
    title: '发票金额',
    dataIndex: 'invoiceAmount',
    align: 'center',
    customRender: ({text}) => text != null ? `${text}元` : '',
  },
];

// 计算发票金额合计
const calculateInvoiceTotal = () => {
  if (!currentDetailRecord.value) return '0';
  const invoiceData = getInvoiceData();
  const total = invoiceData.reduce((sum: number, item: any) => sum + (item.invoiceAmount || 0), 0);
  return `${total}元`;
};

// 获取发票数据
const getInvoiceData = () => {
  if (!currentDetailRecord.value || !currentDetailRecord.value.attachmentFiles) {
    return [];
  }
  // 这里根据实际业务逻辑筛选发票数据
  // 假设发票类型的 type 为特定值，需要根据实际情况调整
  return currentDetailRecord.value.attachmentFiles
      .filter((file: any) => file.type === 2) // 假设type=2为发票类型
      .map((file: any, index: number) => ({
        key: index,
        invoiceNumber: file.invoiceNumber || `85645433${index}`,
        invoiceDate: file.invoiceDate || '2025.1.21',
        invoiceAmount: file.invoiceAmount || 15000,
      }));
};

//上传付款单
const PaymentOrderVisible = ref(false)
//选择的结算单
const settlementList = ref()

const {
  data: PaymentOrderData,
  run: PaymentOrderlist,
  loading: paymentOrderLoading,
  current: paymentOrderCurrent,
  pageSize: paymentOrderPageSize,
} = usePagination(paymentFromApi.getBalnceList);

const handlePaymentOrder = () => {
  PaymentOrderlist({
    startTime: paymentOrderSearchParam.value.startTime,
    endTime: paymentOrderSearchParam.value.endTime,
    merchantCode: paymentOrderSearchParam.value.merchantCode,
    pageNum: 1,
    pageSize: 10
  })
}

// PaymentOrder表格分页
const paymentOrderPagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: PaymentOrderData.value?.total || 0,
  current: PaymentOrderData.value?.pageNum || 1,
  pageSize: PaymentOrderData.value?.pageSize || 10,
  style: {justifyContent: 'center'},
}));

// 处理PaymentOrder表格分页变化
const handlePaymentOrderTableChange = (
    pag: any,
    filters?: any,
    sorter?: any,
) => {
  PaymentOrderlist({
    startTime: paymentOrderSearchParam.value.startTime,
    endTime: paymentOrderSearchParam.value.endTime,
    merchantCode: paymentOrderSearchParam.value.merchantCode,
    pageNum: pag.current || 1,
    pageSize: pag.pageSize || 10,
  });
};
const rowSelection: TableProps['rowSelection'] = {
  onChange: (selectedRowKeys: Key[], selectedRows: DataType[]) => {
    settlementList.value = selectedRows
  },
};
//订单
const PaymentOrderColumns: ColumnType<DataType>[] = [
  {
    title: '会议单号',
    dataIndex: 'mainCode',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '会议时间',
    dataIndex: 'meetingTime',
    width: '200px',
    align: 'center',
    customRender: ({record}) => {
      if (record.startDate && record.endDate) {
        return `${record.startDate} 至 ${record.endDate}`;
      }
      return '';
    },
  },
  {
    title: '会议负责人',
    dataIndex: 'operatorName',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '结算金额',
    dataIndex: 'amount',
    width: '120px',
    align: 'center',
    customRender: ({text}) => text != null ? `${text}元` : '',
  },
  {
    title: '服务费率',
    dataIndex: 'feeRate',
    width: '100px',
    align: 'center',
    customRender: ({text}) => text != null ? `${text}%` : '',
  },
  {
    title: '收款金额',
    dataIndex: 'rceiveAmount',
    width: '120px',
    align: 'center',
    customRender: ({text}) => text != null ? `${text}元` : '',
  },
];
// 文件上传处理
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
      .upload(formData)
      .then((it) => {
        options.file.filePath = baseUrl + it.path;
        options.file.fileName = options.file.name;
        options.onProgress(100);
        options.onSuccess(it, options.file);

        // 确保文件被添加到列表中
        if (!fileList.value.some((f) => f.fileName === options.file.name)) {
          fileList.value.push(options.file);
        }
      })
      .catch((error) => {
        console.error('上传失败:', error);
        message.error('文件上传失败，请重试');
      })
      .finally(() => {
        uploadLoading.value = false;
      });
};

// 文件删除处理
const handleFileRemove = (file: any) => {
  const index = fileList.value.findIndex(f => f.uid === file.uid);
  if (index > -1) {
    fileList.value.splice(index, 1);
  }
};

// 提交上传的付款凭证
const submitUpload = () => {
  if (fileList.value.length === 0) {
    message.error('请先上传付款凭证');
    return;
  }

  if (!currentDetailRecord.value?.id) {
    message.error('记录ID不存在');
    return;
  }

  // 提取文件路径
  const attachmentFiles = fileList.value.map((file) => file.filePath).filter(Boolean) as string[];

  if (attachmentFiles.length === 0) {
    message.error('文件上传未完成，请重试');
    return;
  }

  uploadLoading.value = true;

  // 调用confirmPaymentPayment接口
  paymentFromApi.confirmPaymentPayment({
    id: currentDetailRecord.value.id,
    attachmentFile: attachmentFiles
  }).then(() => {
    message.success('付款凭证上传成功');
    handleView(); // 关闭弹窗
    // 刷新列表
    listApiRun({
      ...searchParam.value,
      pageNum: data.value?.pageNum || 1,
      pageSize: data.value?.pageSize || 10,
    });
  }).catch((error) => {
    console.error('提交付款凭证失败:', error);
    message.error('提交失败，请重试');
  }).finally(() => {
    uploadLoading.value = false;
  });
};

// 驳回操作
const submitReject = () => {
  if (!ReasonsRejection.value.trim()) {
    message.error('请填写驳回原因');
    return;
  }

  if (!currentDetailRecord.value?.id) {
    message.error('记录ID不存在');
    return;
  }

  uploadLoading.value = true;

  // 调用rejectPayment接口
  paymentFromApi.rejectPayment({
    id: currentDetailRecord.value.id,
    attachmentFile: [], // 驳回时可以传空数组
    rejectReason: ReasonsRejection.value.trim() // 传递驳回原因
  }).then(() => {
    message.success('驳回成功');
    handleView(); // 关闭弹窗
    // 刷新列表
    listApiRun({
      ...searchParam.value,
      pageNum: data.value?.pageNum || 1,
      pageSize: data.value?.pageSize || 10,
    });
  }).catch((error) => {
    console.error('驳回失败:', error);
    message.error('驳回失败，请重试');
  }).finally(() => {
    uploadLoading.value = false;
  });
};

// 提交生成付款单
const submitPaymentOrder = () => {
  if (!settlementList.value || settlementList.value.length === 0) {
    message.error('请先选择会议');
    return;
  }

  // 提取选中会议的mainCode列表
  const mainCodes = settlementList.value.map((item: any) => item.mainCode).filter(Boolean);

  if (mainCodes.length === 0) {
    message.error('选中的会议中没有有效的会议单号');
    return;
  }

  // 提取merchantCode并检查是否为同一服务商
  const merchantCodes = settlementList.value.map((item: any) => item.merchantCode).filter(Boolean);
  const uniqueMerchantCodes = [...new Set(merchantCodes)];

  if (uniqueMerchantCodes.length === 0) {
    message.error('选中的会议中没有有效的服务商信息');
    return;
  }

  if (uniqueMerchantCodes.length > 1) {
    message.error('只能选择同一个服务商的会议进行生成缴费单');
    return;
  }

  // 调用生成付款单接口
  paymentFromApi.createPayment({
    mainCodes: mainCodes,
    merchantCode: uniqueMerchantCodes[0] as string
  }).then(() => {
    message.success('缴费单生成成功');
    closePaymentOrderModal();
    // 刷新列表
    listApiRun({
      ...searchParam.value,
      pageNum: data.value?.pageNum || 1,
      pageSize: data.value?.pageSize || 10,
    });
  }).catch((error) => {
    console.error('生成缴费单失败:', error);
    message.error('生成缴费单失败，请重试');
  });
};

// 处理菜单点击事件
const handleMenuClick = (record: any, e: MenuInfo) => {
  const key = e.key as string;
  switch (key) {
    case 'delete':
      handleDelete(record.id);
      break;
    case 'upload':
      openUploadModal(record);
      break;
    case 'uploadInvoice':
      openInvoiceModal(record);
      break;
    default:
      break;
  }
};

// 计算菜单选项
const getMenuOptions = (record: any) => {
  const options: MenuItemType[] = [];

  // 根据状态添加不同的操作选项
  if (record.status === PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD) {
    // 待上传发票：显示删除和上传发票按钮
    options.push({
      key: 'delete',
      label: '删除',
    });
    options.push({
      key: 'uploadInvoice',
      label: '上传发票',
    });
  } else if (record.status === PaymentFromStatusEnum.CONFIRM_PAYMENT) {
    // 确认付款：显示上传付款凭证按钮
    options.push({
      key: 'upload',
      label: '上传付款凭证',
    });
  }
  // 已完成状态只显示查看，不需要额外的菜单选项

  return options;
};

// 生成付款单
const generatePaymentOrder = () => {
  PaymentOrderVisible.value = true
  // 打开弹窗时就调用接口获取数据
  PaymentOrderlist({
    startTime: paymentOrderSearchParam.value.startTime,
    endTime: paymentOrderSearchParam.value.endTime,
    merchantCode: paymentOrderSearchParam.value.merchantCode,
    pageNum: 1,
    pageSize: 10
  })
};

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="3" style="text-align: right;padding-right: 10px;">
            <label for="createTime">缴费单创建时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="beginAndEnd" value-format="YYYY-MM-DD" style="width: 100%" allow-clear/>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="serviceProvider">服务商：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.merchantCode" placeholder="请输入服务商" allow-clear/>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="status">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select v-model:value="searchParam.status" placeholder="请选择状态" allow-clear style="width: 100%">
              <h-select-option :value="PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD] }}
              </h-select-option>
              <h-select-option :value="PaymentFromStatusEnum.CONFIRM_PAYMENT">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.CONFIRM_PAYMENT] }}
              </h-select-option>
              <h-select-option :value="PaymentFromStatusEnum.COMPLETED">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.COMPLETED] }}
              </h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined/>
              查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
            <h-button type="primary" @click="generatePaymentOrder">
              <PlusOutlined/>
              生成缴费单
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
                 :pagination="pagination" :scroll="{ y: 550 }" :loading="loading"
                 @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'status'">
              <h-tag :color="PaymentFromStatusTagColorMap[record.status as keyof typeof PaymentFromStatusTagColorMap]">
                {{ PaymentFromStatusMap[record.status as keyof typeof PaymentFromStatusMap] || '未知状态' }}
              </h-tag>
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <div class="operator-buttons">
                <h-button type="link" @click="handleView(record)">查看</h-button>
                <Actions v-if="getMenuOptions(record).length > 0" :menu-options="getMenuOptions(record)"
                         :on-menu-click="(e) => handleMenuClick(record, e)">
                </Actions>
              </div>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
    <!-- 生成付款单弹窗 -->
    <Modal v-model:open="PaymentOrderVisible" title="生成缴费单" :footer="null" @cancel="closePaymentOrderModal"
           width="60%">
      <div>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="4" style="text-align: right;padding-right: 10px;">
            <label for="createTime">会议创建时间：</label>
          </h-col>
          <h-col :span="6">
            <h-range-picker v-model:value="paymentOrderBeginAndEnd" value-format="YYYY-MM-DD" style="width: 100%"
                            allow-clear/>
          </h-col>
          <h-col :span="3" style="text-align: right;padding-right: 10px;">
            <label for="merchantCode">服务商名称：</label>
          </h-col>
          <h-col :span="5">
            <h-input v-model:value="paymentOrderSearchParam.merchantCode" placeholder="支持名字和V码搜索" allow-clear/>
          </h-col>
          <h-col :span="6" style="text-align: right;">
            <h-button type="primary" @click="handlePaymentOrder()">
              <SearchOutlined/>
              查询
            </h-button>
          </h-col>
        </h-row>
        <a-table :row-selection="rowSelection" :columns="PaymentOrderColumns"
                 :data-source="PaymentOrderData?.records || []"
                 :loading="paymentOrderLoading" :pagination="paymentOrderPagination"
                 @change="handlePaymentOrderTableChange" style="margin-top: 15px;">
          <template #bodyCell="{ column, text }">

          </template>
        </a-table>
        <div style="text-align: right; margin-top: 20px;">
          <h-button style="margin-right: 10px;" @click="closePaymentOrderModal">取消</h-button>
          <h-button type="primary" @click="submitPaymentOrder" :loading="paymentOrderLoading">生成付款单</h-button>
        </div>
      </div>
    </Modal>

    <!-- 删除了独立的上传付款凭证弹窗，已合并到详情弹窗中 -->

    <!-- 上传发票弹窗 -->
    <Modal v-model:open="invoiceModalVisible" title="上传发票" :footer="null" @cancel="closeInvoiceModal" width="500px">
      <div style="padding: 20px 0;">
        <div style="margin-bottom: 16px;">
          <strong>收款单号：</strong>{{ currentInvoiceRecord?.receivePaymentCode }}
        </div>
        <div style="margin-bottom: 16px;">
          <strong>服务商名称：</strong>{{ currentInvoiceRecord?.merchantName }}
        </div>

        <div style="margin-bottom: 16px;">
          <label style="font-weight: bold;">发票类型：</label>
          <h-select v-model:value="invoiceForm.invoiceType" style="width: 100%; margin-top: 8px;">
            <h-select-option :value="1">国旅</h-select-option>
            <h-select-option :value="2">服务商</h-select-option>
          </h-select>
        </div>

        <div style="margin-bottom: 16px;">
          <label style="font-weight: bold;">发票日期：<span style="color: red;">*</span></label>
          <h-date-picker
              v-model:value="invoiceForm.invoiceDate"
              style="width: 100%; margin-top: 8px;"
              placeholder="请选择发票日期"
              value-format="YYYY-MM-DD"/>
        </div>

        <div style="margin-bottom: 16px;">
          <label style="font-weight: bold;">发票号：<span style="color: red;">*</span></label>
          <h-input
              v-model:value="invoiceForm.invoiceNumber"
              placeholder="请输入发票号"
              style="margin-top: 8px;"/>
        </div>

        <div style="margin-bottom: 16px;">
          <label style="font-weight: bold;">发票金额：<span style="color: red;">*</span></label>
          <h-input-number
              v-model:value="invoiceForm.invoiceAmount"
              placeholder="请输入发票金额"
              style="width: 100%; margin-top: 8px;"
              :min="0"
              :precision="2"/>
        </div>

        <div style="text-align: right; margin-top: 20px;">
          <h-button style="margin-right: 10px;" @click="closeInvoiceModal">取消</h-button>
          <h-button type="primary" @click="submitInvoice" :loading="invoiceLoading">确定</h-button>
        </div>
      </div>
    </Modal>

    <!-- 缴费单详情/上传付款凭证弹窗 -->
    <Modal
        v-model:open="detailVisible"
        :title="viewMode === 'view' ? '缴费单详情' : '上传付款凭证'"
        :footer="null"
        @cancel="handleView()"
        width="800px"
        :loading="detailLoading">
      <div v-if="currentDetailRecord" style="padding: 20px 0;">
        <!-- 基本信息 -->
        <div style="margin-bottom: 20px;">
          <div style="margin-bottom: 12px;">
            <strong>收款单号：</strong>{{ currentDetailRecord.receivePaymentCode }}
          </div>
          <div style="margin-bottom: 12px;">
            <strong>服务商名称：</strong>{{ currentDetailRecord.merchantName }}
          </div>
          <div style="margin-bottom: 12px;">
            <strong>付款总金额：</strong>{{ currentDetailRecord.totalAmount }}元
          </div>
          <!-- 查看模式：显示已上传的付款凭证 -->
          <div v-if="viewMode === 'view'" style="margin-bottom: 12px;">
            <strong>付款凭证：</strong>
            <template v-if="currentDetailRecord.attachmentFiles && currentDetailRecord.attachmentFiles.length > 0">
              <template v-for="(file, index) in currentDetailRecord.attachmentFiles" :key="index">
                <a :href="file.path" target="_blank" style="margin-right: 10px; color: #1890ff;">
                  {{ file.path ? file.path.split('/').pop() || `付款凭证${index + 1}` : `付款凭证${index + 1}` }}
                </a>
              </template>
            </template>
            <span v-else>无</span>
          </div>
        </div>

        <!-- 上传模式：显示上传区域和驳回原因 -->
        <div v-if="viewMode === 'upload'" style="margin-bottom: 20px;">
          <div style="margin-bottom: 16px;">
            <label style="font-weight: bold;">付款凭证：</label>
            <h-upload
                v-model:fileList="fileList"
                :custom-request="uploadRequest"
                :multiple="true"
                :max-count="5"
                @remove="handleFileRemove"
                accept=".pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx"
                :show-upload-list="true">
              <h-button :loading="uploadLoading">
                <UploadOutlined/>
                上传文件
              </h-button>
            </h-upload>
          </div>
          <div style="margin-bottom: 16px;">
            <label style="font-weight: bold;">驳回原因：</label>
            <a-textarea
                v-model:value="ReasonsRejection"
                show-count
                :maxlength="200"
                placeholder="请填写驳回原因（驳回时必填）"
                style="margin-top: 8px;"/>
          </div>
        </div>

        <!-- Tab页 -->
        <a-tabs v-model:activeKey="activeKey">
          <a-tab-pane key="1" tab="订单">
            <h-table
                :columns="detailOrderColumns"
                :data-source="currentDetailRecord.receivePaymentRecordsDetails || []"
                :pagination="false"
                size="small"
                bordered
                style="margin-top: 10px;">
            </h-table>
          </a-tab-pane>
          <a-tab-pane key="2" tab="发票">
            <div style="margin-bottom: 16px;">
              <strong>发票金额合计：</strong>{{ calculateInvoiceTotal() }}
            </div>
            <h-table
                :columns="detailInvoiceColumns"
                :data-source="getInvoiceData()"
                :pagination="false"
                size="small"
                bordered
                style="margin-top: 10px;">
            </h-table>
          </a-tab-pane>
        </a-tabs>

        <!-- 底部按钮 -->
        <div style="text-align: right; margin-top: 20px;">
          <h-button style="margin-right: 10px;" @click="handleView()">取消</h-button>
          <!-- 查看模式：只显示确定按钮 -->
          <h-button v-if="viewMode === 'view'" type="primary" @click="handleView()">确定</h-button>
          <!-- 上传模式：显示确定和驳回按钮 -->
          <template v-if="viewMode === 'upload'">
            <h-button
                style="margin-right: 10px;"
                @click="submitReject"
                :loading="uploadLoading"
                danger>
              驳回
            </h-button>
            <h-button
                type="primary"
                @click="submitUpload"
                :loading="uploadLoading">
              确定
            </h-button>
          </template>
        </div>
      </div>
    </Modal>

  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.operator-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.operator-buttons :deep(.ant-btn) {
  padding: 0 4px;
  font-size: 14px;
}
</style>
