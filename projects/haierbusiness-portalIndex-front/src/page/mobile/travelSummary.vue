<template>
  <div class="app-container">
    <!--  :pagination="{ clickable: true }" -->
    <swiper :noSwiping="true" @swiper="onSwiper" @slideChange="onSlideChange" :speed="1000" :effect="'fade'"
      direction="vertical" :modules="modules" :mousewheel="{ mousewheel: true }" :autoplay="false">
      <swiper-slide>
        <div class="item swiper-bg-1">
          <!-- 
          <div class="showText">
            <p v-if="showText1" class="title animate__animated animate__bounce showText1">
              1、集团差旅业务总览
            </p>
            <p  v-if="showText1" class="animate__animated animate__bounce showText1">
              2024年，海尔商旅火力全开，为集团的小伙伴们安排了
              <span class="tip">{{ travelSummary.travelCount }}</span> 次说走就走的出差之旅！
            </p>
            <p  v-if="showText2" class="animate__animated animate__backInLeft showText2">
              机票预订量狂飙至
              <span class="tip">{{ travelSummary.planeCount }}</span>次，酒店预订更是高达
              <span class="tip">{{ travelSummary.hotelCount }}</span>，打车服务也轻松破
              <span class="tip">{{ travelSummary.taxiCount }}</span>次，火车票预订也是稳稳地
              <span class="tip">{{ travelSummary.trainCount }}</span>张。
              海尔人的脚步遍布全国
              <span class="tip">{{ travelSummary.provinceCount }}</span>个城市，还跨出国门，游历了全球
              <span class="tip">{{ travelSummary.countryCount }}</span>个国家。
            </p>
            <p  v-if="showText3" class="animate__animated animate__bounce showText3">
              每一次出行，都是海尔人对工作的热爱与品质追求的完美诠释。商务服务平台商旅链群统一集采差旅资源，实现资源最大化利用，全年节省
              <span class="tip">{{ travelSummary.saveAmount }}</span>！更有免费机场休息区、优先选座特权、因私出行专区、特价停车场等贴心服务，让您的差旅生活更加惬意。
            </p>
          </div> -->


          <div class="content">
            <div class="flex center " v-if="travelHabitShow">
              <div>您是</div>
              <div v-if="showText1" class="ml-10 type title animate__animated animate__bounce showText1">
                {{ travelSummary?.travelHabit }}
                <div class="bottom-bg"></div>
              </div>
            </div>

            <div class="flex center">
              <div class="mr-5">数据显示您</div>
              <i class="four-day">{{ `提前4天创建出差申请单(<5天)` }} </i>
            </div>

            <div class="flex center">
              <div class="mr-5">的占比高达</div>
              <!-- <div v-show="showText2" class="zb animate__animated animate__backInLeft showText2">{{ travelSummary.advanceRate }}%</div> -->

              <div class="zb">{{ travelSummary.advanceRate }}%</div>
            </div>

            <div class="flex center">
              <div>虽然临时出差在所难免</div>
            </div>

            <div class="flex center">
              <div>但提前规划能让您的旅途更加从容</div>
            </div>

            <div class="flex center">
              <div class="computer mr-5"></div>
              <i class="blue">商务服务平台智慧大脑AI系统</i>
            </div>

            <div class="flex center">
              <div>时刻在线</div>
            </div>

            <div class="flex center">
              <div>记录您的差旅行为,分析数据</div>
            </div>

            <div class="flex center">
              <div>为各领域产业提供</div>
              <i class="blue">降费秘籍</i>
            </div>

            <div class="flex center">
              <div>培养合规差旅习惯</div>
            </div>

            <div class="flex center">
              <div>助力集团差旅管理升级</div>
              <i class="blue">实现降本增效</i>
              <div class="up"></div>
            </div>
          </div>
        </div>
      </swiper-slide>

      <swiper-slide v-if="travelSummary?.userSavedAmountAll != '0元'">
        <div class="item swiper-bg-2">
          <div class="content">
            <div class="money-box flex center">

              <div class="flex ">
                <div>根据海尔商旅数据,您</div>
                <i class="blue ml-5">{{ travelSummary.year + travelSummary.reportScopeName }}</i>
              </div>

              <div class="flex center">
                <div>为集团节省了</div>
              </div>

              <div class="flex center">
                <div class="money-bg mr-5"></div>
                <i class="blue money">{{ travelSummary.userSavedAmountAll }}</i>
              </div>

            </div>

            <div class="flex center">
              <div>其中,</div>
              <div class="hotel mr-5"></div>
              <i class="mr-5 blue">酒店合住</i>
              <div class="mr-10">贡献</div>
              <i class="blue">{{ travelSummary.userSavedAmountHotel }}</i>
            </div>

            <div class="flex center">
              <div class="mr-10">提前4天预订节省</div>
              <i class="blue ">{{ travelSummary.userSavedAmountPlane }}</i>
            </div>

            <div class="flex center">
              <div>海尔商旅将根据您出行节省金额</div>
            </div>

            <div class="flex center">
              <div>通过</div>
              <i class="blue mr-5">节约归己</i>
              <div>的方式按照比例分享至您个人</div>
            </div>

            <div class="flex center">
              <div>以此鼓励您出色的差旅行为!</div>
            </div>


          </div>
        </div>
      </swiper-slide>

      <swiper-slide :class="travelSummary?.userSavedAmountAll == '0元' ? 'onlySaving' : ''"
        v-if="travelSummary?.userSavingAmountAll != '0元'">
        <div class="item swiper-bg-3">

          <div class="content">

            <div class="flex center">
              <div class="but orange">不过!</div>
            </div>

            <div class="flex center">
              <div class="mr-10">您还有</div>
              <i class="blue mr-5">{{ travelSummary.userSavingAmountAll }}</i>
              <div>的开支可以再省省哦!</div>
            </div>

            <div class="flex center">
              <div class="mr-10 calendar"></div>
              <i class="blue mr-5">未提前预定</i>
              <div class="mr-10">损失</div>
              <i class="orange ">{{ travelSummary.userSavingAmountPlaneAdvance }}</i>
            </div>

            <div class="flex center">
              <div class="mr-10 gaiqian"></div>
              <i class="blue mr-5">退改签</i>
              <div class="mr-10">又花了</div>
              <i class="orange ">{{ travelSummary.userSavingAmountHotelModify }}</i>
            </div>

            <div class="flex center">
              <i class="blue ">机票没抢到前后一小时最低价</i>
            </div>

            <div class="flex center">
              <div class="mr-10">也亏了</div>
              <i class="orange ">{{ travelSummary.userSavingAmountPlaneHour }}</i>
            </div>

            <div class="flex center">
              <div>钞票如流水,且省且珍惜!</div>
            </div>

            <div class="flex center">
              <div>海尔商旅已将您的可再节省打包成差旅报告</div>
            </div>

            <div class="flex center">
              <div>发送至您,</div>
              <i class="blue ">赋能差旅管理新高度 !</i>
            </div>

          </div>

        </div>
      </swiper-slide>

      <div class="pre-btn" v-if="homeSwiper?.activeIndex != 0" @click="preBtnClick"></div>
      <div class="next-btn" v-if="showNextBtn" @click="nextBtnClick"></div>

    </swiper>
  </div>
</template>
  
<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';

// 导入swiper组件
import { Swiper, SwiperSlide } from "swiper/vue";
// 引入swiper样式（按需导入）
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/scrollbar";
import "swiper/css/autoplay";
import "swiper/css/free-mode";
// 引入swiper核心和所需模块
import {
  Mousewheel,
  Navigation,
  Pagination,
  Scrollbar,
  A11y,
  Autoplay,
  FreeMode,
  EffectFade,
  EffectFlip,
  EffectCards,
  EffectCreative,
  EffectCoverflow
} from "swiper/modules";
import { onMounted, ref, computed } from "vue";
import {
  MerchantTypeConstant,
  ITriveFilter
} from "@haierbusiness-front/common-libs";
import { travelSummaryApi } from "@haierbusiness-front/apis";
import router from '../../router';

const modules = [
  Mousewheel,
  Pagination,
  Autoplay,
  FreeMode,
  Scrollbar,
  Navigation,
  EffectFade,
  EffectFlip,
  EffectCards,
  EffectCreative,
  EffectCoverflow
];
// swiper实例
const homeSwiper = ref(null);
const store = applicationStore();

const { loginUser } = storeToRefs(store);

const showNextBtn = computed(() => {
  return homeSwiper.value?.activeIndex != homeSwiper.value?.slides?.length - 1;
});

// 初始化
const onSwiper = swiper => {
  homeSwiper.value = swiper;
};
const showText1 = ref<boolean>(false)
const showText2 = ref<boolean>(false)
const showText3 = ref<boolean>(false)
const clear = () => {
  showText1.value = false
  showText2.value = false
  showText3.value = false
}
const showOneSwiper = () => {
  setTimeout(() => {
    showText1.value = true
  }, 500)
  showText1.value = true
  setTimeout(() => {
    showText2.value = true
  }, 1500)
  setTimeout(() => {
    showText3.value = true
  }, 3000)
}

const preBtnClick = () => {
  homeSwiper.value.slidePrev();
  console.log("??????????????", homeSwiper.value)
}

const nextBtnClick = () => {
  homeSwiper.value.slideNext();
}
// swiper切换时触发
const onSlideChange = swiper => {
  setTimeout(() => {
    clear()
    showOneSwiper()
  }, 500)
  console.log(swiper)
};


const travelHabitShow = ref(true)
const isPowerUserList = ref([
  '22069502', '01347713', '00594761', '00880370', '01306872', '00012683', '01484801', '00880045', '01062839',
])
const isTravelHabitShowUserList = ref([
  '00880367', '00910069', '00920102'
])
const travelSummary = ref<ITriveFilter>({});
// 获取报表数据
const getReportDetail = () => {
  travelSummaryApi
    .getReportDetail({ travelSumReportDetailId: currentRouter.value?.currentRoute.query.travelSumReportDetailId, isShowOtherData: currentRouter.value?.currentRoute.query.isShowOtherData })
    .then((res: ITriveFilter) => {
      travelSummary.value = res;
    });
};

// 是否已读
const readNotify = () => {
  travelSummaryApi.readNotify({ travelSumReportDetailId: currentRouter.value?.currentRoute.query.travelSumReportDetailId }).then((res: any) => {
    // avatar.value = res.avatar?.avatar_240
  })
}

const currentRouter = ref<any>('')
onMounted(async () => {
  currentRouter.value = await router
  if (isPowerUserList.value.includes(loginUser.value.username) || isTravelHabitShowUserList.value.includes(loginUser.value.username)) {
    travelHabitShow.value = false
  }
  getReportDetail()
  showOneSwiper()
  readNotify()
})
</script>
  
<style lang="scss" scoped>
.app-container {
  width: 100%;
  height: 100vh;
}






@media screen and (max-width: 425px) {
  .app-container {
    width: 100%;
  }

}


.item {
  width: 100%;
  height: 100%;

  // display: flex;
  // justify-content: center;
  // align-items: center;
  p {
    color: #fff;
    font-weight: 700;
  }

  .name {
    font-size: 100px;
    color: #fff;
  }

}

.swiper {
  width: 100%;
  height: 100%;
}




/* 渐隐动画 */
.swiper-slide {
  opacity: 0 !important;
  transition: opacity 1.5s ease-in-out;
}

.swiper-slide-active {
  opacity: 1 !important;
}

// 文字动效
.staggered-fade-enter-active,
.staggered-fade-leave-active {
  transition: all 0.8s ease;
}

.staggered-fade-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.paragraph {
  margin: 1em 0;
  line-height: 1.6;
}

.item {
  overflow: hidden;
  position: relative;

  .content {
    position: absolute;
    // width: 700px;
    width: 86%;
    max-width: 700px;
    bottom: 90px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    padding: 132px 32px 40px 32px;
    flex-direction: column;
    align-items: center;
    color: #111111;
    font-size: 30px;
    font-family: Source Han Sans CN;
  }
}

.flex {
  display: flex;
}

.blue {
  color: #286AE3;
  font-weight: 700;
}

.orange {
  color: #FF5E11;
  font-weight: 700;

}

.center {
  justify-content: center;
  align-items: center;
}

.ml-10 {
  margin-left: 10px;
}

.mr-10 {
  margin-right: 10px;

}

.ml-5 {
  margin-left: 5px;

}

.mr-5 {
  margin-right: 5px;

}

.pre-btn {
  position: absolute;
  z-index: 9999999999;
  background-image: url("@/assets/image/summary/icon_up.png");
  background-size: cover;

  top: 70px;
  left: 50%;
  width: 70px;
  height: 70px;
  transform: translateX(-50%);
}

.next-btn {
  position: absolute;
  z-index: 9999999999;

  background-image: url("@/assets/image/summary/icon_next.png");
  background-size: cover;

  bottom: 20px !important;

  width: 70px;
  height: 70px;
  left: 50%;
  transform: translateX(-50%);
}

.showText {
  width: 60%;
  font-size: 24px;
  margin-top: 300px;
  margin-left: 40%;
}

.swiper-bg-1 {
  background-image: url("@/assets/image/summary/swiper_bg_1.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .content {
    height: 900px;
    max-height: 62%;
    background-image: url("@/assets/image/summary/bg_card1.png");
    background-size: 100% 100%;
    justify-content: space-around;

    .type {

      font-family: Source Han Sans CN;
      font-weight: 700;
      font-size: 40px;
      color: #286AE3;
      line-height: 60px;
      position: relative;

      .bottom-bg {
        height: 14px;
        width: 100%;
        position: absolute;
        background: rgba(41, 107, 227, 15%);
        bottom: 4px;
      }
    }

    .four-day {
      color: #286AE3;
      font-family: SourceHanSansCN-Regular;
      font-weight: 700;
    }

    .zb {
      font-family: Impact;
      font-weight: 700;
      font-size: 50px;
      color: #FF6A1A;
      line-height: 52px;
      font-style: italic;
    }

    .computer {
      width: 68px;
      height: 58px;
      background-image: url("@/assets/image/summary/pic_diannao.png");
      background-size: cover;
    }

    .up {
      width: 74px;
      height: 65px;
      background-image: url("@/assets/image/summary/pic_tisheng.png");
      background-size: cover;
      transform: translate(-30px, 10px);
    }
  }

}

.swiper-bg-2 {
  background-image: url("@/assets/image/summary/swiper_bg_2.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .content {
    height: 815px;
    max-height: 58%;
    background-image: url("@/assets/image/summary/bg_card2.png");
    background-size: 100% 100%;
    justify-content: space-around;

    .money-box {
      width: 540px;
      height: 230px;
      padding: 20px;
      background: #E7EFFF;
      border-radius: 20px;
      flex-direction: column;
      justify-content: space-around;
    }

    .money-bg {
      width: 47px;
      height: 44px;
      background-image: url("@/assets/image/summary/pic_jinbi.png");
      background-size: cover;
    }

    .money {
      font-family: Source Han Sans CN;
      font-weight: 700;
      font-size: 36px;
      color: #296AE3;
    }

    .hotel {
      width: 52px;
      height: 44px;
      background-image: url("@/assets/image/summary/pic_jiudian.png");
      background-size: cover;
    }
  }

}

.swiper-bg-3 {
  background-image: url("@/assets/image/summary/swiper_bg_3.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .content {
    height: 840px;
    max-height: 58%;
    background-image: url("@/assets/image/summary/bg_card3.png");
    background-size: 100% 100%;
    justify-content: space-around;

    .but {
      font-size: 40px;

    }

    .calendar {
      width: 41px;
      height: 45px;
      background-image: url("@/assets/image/summary/pic_yuding.png");
      background-size: cover;
    }

    .gaiqian {
      width: 26px;
      height: 43px;
      background-image: url("@/assets/image/summary/pic_gaiqian.png");
      background-size: cover;
    }
  }
}

.onlySaving .swiper-bg-3 .content {
  background-image: url("@/assets/image/summary/swiper_bg_3_onlySaving.png") !important;
}


@media (min-width: 426px) and (max-width: 1920px) {
  .app-container {
    width: 425px;
    margin: 0 auto;
  }

  .item {
    .content {
      font-size: 14px !important;
    }
  }

  .swiper-bg-1 {
    .content {

      .money-box {
        width: 40%;
      }

      .zb {
        font-size: 26px;
      }
    }
  }

  .swiper-bg-2 {
    .content {

      .money-box {
        width: 90%;
        height: 40%;
      }
    }
  }

  .swiper-bg-3 {
    .content {
      .but {
        font-size: 26px;

      }
    }

  }

}
</style>
