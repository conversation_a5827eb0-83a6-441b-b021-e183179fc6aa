<script setup lang="ts">
import { Carousel as hCarousel, message, Modal } from 'ant-design-vue';
import { onMounted, ref, computed, watch,h,createVNode } from 'vue';
import { LeftOutlined, RightOutlined, DownOutlined } from '@ant-design/icons-vue';
import { storeToRefs } from 'pinia';
import dayjs from 'dayjs';

import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation, Pagination, Scrollbar, A11y } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/scrollbar';
import { advertisementListApi, loginApi, teamApi,informationApi, lifeListApi, cityApi,rechargeApi } from '@haierbusiness-front/apis';
import { usePagination, useRequest } from 'vue-request';
import { removeStorageItem } from '@haierbusiness-front/utils';
import {
  HeaderConstant
} from '@haierbusiness-front/common-libs'
import { guid } from '@haierbusiness-front/utils';
import applyNoCom from './components/apply/applyNo.vue';
import travelerSelect from './components/apply/travelerSelect.vue';
import userSelect from './components/apply/userSelect.vue';

import city from './components/apply/city.vue';
import teamCity from './components/apply/teamCity.vue';
import trainCity from './components/apply/trainCity.vue';

import airportCity from './components/apply/airportCity.vue';

import internationalAirportCity from './components/apply/internationalAirportCity.vue';


import datePicker from './components/apply/datePicker.vue';
import datePickerTeam from './components/apply/datePickerTeam.vue';

import airportSelect from './components/apply/airportSelect.vue';
import serviceDatePicker from './components/apply/serviceDatePicker.vue';
import destinationLevel from './components/apply/destinationAndLevel.vue';
import checkInOut from './components/apply/checkInOut.vue';
import keyword from './components/apply/keyword.vue';
import hotel from '@/assets/image/banner/hotel.png';
import hotel1 from '@/assets/image/banner/hotel1.png';
import hotel2 from '@/assets/image/banner/hotel2.png';
import hotel3 from '@/assets/image/banner/hotel3.png';
import router from '../../router';
import activity from '../card/activity.vue';
import youXuan from '../card/youxuan.vue';
import apply from '@/assets/image/banner/ban-index.png';
import air from '@/assets/image/banner/ban-air.png';
import hotelIcon from '@/assets/image/banner/ban-hotel.png';
import train from '@/assets/image/banner/ban-train.png';
import us from '@/assets/image/banner/ban-us.png';
import voidImg from '@/assets/image/banner/void.png';
import voidAct from '@/assets/image/banner/void-act.png';
import withdraw from '@/assets/image/banner/withdraw.png';
import withdrawAct from '@/assets/image/banner/withdraw-act.png';
import change from '@/assets/image/banner/change.png';
import changeAct from '@/assets/image/banner/change-act.png';
import applyBackground from '@/assets/image/banner/background/apply.png';
import flightBackground from '@/assets/image/banner/background/flight.png';
import hotelBackground from '@/assets/image/banner/background/hotel.png';
import trainBackground from '@/assets/image/banner/background/train.png';
import { tripApi } from '@haierbusiness-front/apis';
import { getEnumOptions } from '@haierbusiness-front/utils';
import {
  QuestionCircleOutlined,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  DeleteOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  CheckCircleTwoTone,
  ExclamationCircleOutlined,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
} from '@ant-design/icons-vue';
import {
  Modal as hModal,
  message as hMessage,
  Button as hButton,
  Col as hCol,
  Select as hSelect,

  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  RangePicker as hRangePicker,
  Row as hRow,
  Tag as hTag,
  Spin as hSpin,
  Empty as hEmpty,
  Space as hSpace,
  Pagination as hPagination,
  Checkbox as hCheckbox,
  Alert as hAlert,
  SelectOption as hSelectOption,
} from 'ant-design-vue';
import {
  TripApprovalStatus,
  TripDocumentStatus,
  TripChangeStatus,
  TripBudgeStatus,
  ICreatTrip,
  ITraveler,
  TripApprovalStatusToTagColor,
  TripDocumentStatusToTagColor,
  TripChangeStatusToTagColor,
  IDataListItem,
  TCteateTeam,
  CityItem,
  IUserInfo
} from '@haierbusiness-front/common-libs';
const currentRouter = ref();

onMounted(async () => {
  // 获取权限
  checkBeta()


  currentRouter.value = await router;
  onFilterChange();
  if(localStorage.getItem('reimburseData')) {
    activeApplyData.value = JSON.parse(localStorage.getItem('reimburseData'));
    reimburseUserCode.value = JSON.parse(localStorage.getItem('reimburseCode'));
    goToEES()
  }
});
const showOldIndex = ref(false)
// 查询当前用户是否有测试权限
const checkBeta = () => {
  tripApi.checkBeta().then((res:any) => {
    console.log(11111, res)
    showOldIndex.value = res
  })
}

// 根据工号查询职级
const getDirectLineByUserCode = async (username:any) => {
  const res = await rechargeApi.getDirectLine({username: username})
  console.log('----根据工号查询职级------>', res.brandCode)
  return res
}

// 根据出行人转换成职级字符串
const convertZJ = async (list?: any) => {
  if(!list || !list.length) {
    return ''
  }
  let usernameList:any = []
  list.forEach((item: any) => {
      usernameList.push(item.travelUserNo || item.username)
  })
  // 使用 Promise.all 并行执行异步操作
  const results = await Promise.all(usernameList.map(username => {
    if (username) {
      return getDirectLineByUserCode(username)
    }else {
      return ''
    }
  }));
  let zjstr = results.map(res => res?.brandCode || '');
  console.log('----根据出行人转换成职级字符串------>', zjstr)
  return zjstr.join(',')
  
}

const covertLx = (list:any) => {
  if(!list || !list.length) {
    return ''
  }
  const res = list.map((item:any) => {
    if(item.travelUserNo) {
      return 1
    }else {
      return 2
    }
  })
  console.log('----根据出行人转换成出行人字符串------>', res)
  return res.join(',')
}

const store = applicationStore();
const { loginUser } = storeToRefs(store);

const tripUrl = import.meta.env.VITE_BUSINESS_TRIP_URL;
const indexUrl = import.meta.env.VITE_BUSINESS_INDEX_URL;
const processUrl = import.meta.env.VITE_BUSINESS_PROCESS_URL;

//#region 推广位，资讯，园区生活

// 推广位
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(advertisementListApi.queryAdvertisement, {
  defaultParams: [
    {
      showStatus: 1,
    },
  ],
  manual: false,
});

const dataSource = computed(() => data.value?.records || []);

// 资讯
const {
  data: infoData,
  run: infoListApiRun,
  loading: infoLoading,
} = useRequest(informationApi.top, {
  defaultParams: [
    {
      showStatus: 1,
    },
  ],
  manual: false,
});

const infoDataSource = computed(() => infoData.value || []);

// 园区生活
const {
  data: lifeData,
  run: lifeListApiRun,
  loading: lifeLoading,
} = useRequest(lifeListApi.life, {
  defaultParams: [
    {
      showStatus: 1,
    },
  ],
  manual: false,
});

const lifeDataSource = computed(() => lifeData.value || []);

const gotoUrlOrDetail = (data: { jumpLinkPc?: string; id?: number }, url: string) => {
  if (data.jumpLinkPc) {
    window.open(data.jumpLinkPc);
  } else {
    if (!data.id) {
      message.error('未对应信息！');
      return;
    }
    const thisUrl = currentRouter.value.resolve({
      path: url,
    });
    window.open(thisUrl.href + '?id=' + data.id);
  }
};


const openNewWindow = (url: string) => {
  window.open(url, '_blank');
};

const modules = ref([Navigation, A11y]);

// hover效果



const isSwiperHover = ref(false);

const setIsSwiperHover = (value: boolean) => {
  isSwiperHover.value = value;
};

//#endregion

//#region 出差申请单
// 顶部五大类
const bannerList = ref([
  {
    url: apply,
    title: '出差申请',
  },
  {
    url: air,
    title: '机票',
  },
  {
    url: hotelIcon,
    title: '酒店',
  },
  {
    url: train,
    title: '火车',
  },
  {
    url: us,
    title: '团队',
  },
]);

// 顶部五大类切换
const mouseEnter = ref(-1);

// 移入哪一个类
const activeBaner = ref(0);

watch(
  activeBaner, 
  (newValue) => {
    creatTripParma.value = {};
    planEndDate.value = '';
    planBeginDate.value = '';
    hotelLeaveDate.value = ''
    hotelInDate.value = ''

      hotelChosedCityName.value=''
      hotelChosedCityCode.value=''

      showApplyNoHelp.value = false
      showAirPortHelp.value = false
      showDateHelp.value = false
  }
);

const showMoreBtn = (data:any) => {
  let showBg = data.status == '30' && data.changeApplyStatus != '20' && data.type != 1
  let showZf = (data.status == '10' || data.status == '20' || data.status == '30')
  let showCh = data.status == '20' && data.auditStatus == '20'
  let showBgCh = data.changeApplyStatus == '20' 

 return data.status != '90' && data.type != 1 && (showBg || showZf || showCh || showBgCh) && data.createBy == loginUser.value?.username  &&  data.originApp != 'HWORK'
}

// 移入
const handelBanMouse = (value: number) => {
  mouseEnter.value = value;
};


// 点击切换某个大类
const handelBan = (value: number) => {
  activeBaner.value = value;
  mouseEnter.value = -1;
  // 还原单程和因公
  travelType.value = 1;
  travelReason.value = 1;
  applyId.value = '';
  applyNo.value = '';
};
// 鼠标移出某个大类
const handelBanMouseLeave = () => {
  mouseEnter.value = -1;
};

// 根据id递归获取城市名
const cityName = ref('');
const getCityName = (code: string, options) => {
  options.forEach((item) => {
    if (item.districts && item.districts.length) {
      getCityName(code, item.districts);
    } else {
      if (code === item.adcode) {
        cityName.value = item.name;
      }
    }
  });
  return cityName.value;
};
let listTemp = [];
const getOutPerson = (list: Array<ITraveler>) => {
  listTemp = [];
  list?.forEach((item) => {
    if (item.mainFlag != '1') {
      listTemp.push(item.travelUserName);
    }
  });
  return listTemp.join(',');
};
const getMainPerson = (list: Array<ITraveler>) => {
  return list?.filter((item) => item.mainFlag == '1')[0]?.travelUserName;
};

//
const applyForm = ref('');
// 当前选中的出差单
const activeApply = ref({});
const cityOptions = ref({});
const searchKey = ref<ICreatTrip>({
  statusList: ['20','30','40'], //单据状态
  pageNum: 1,
  pageSize: 999,
  
});
const dataList = ref<Array<IDataListItem>>([]);
interface ApplyId {
  id?: string;
  applyNo?: string;
}
const applyIdList = ref<Array<ApplyId>>([]);
// 获取出差单
const onFilterChange = async () => {
  // if (!(cityOptions.value && cityOptions.value?.districts && cityOptions.value?.districts.length > 0)) {
  //   cityOptions.value = await tripApi.district();
  // }

  const res = await tripApi.getApplypageList(searchKey.value);
  dataList.value = res?.records || [];
  applyIdList.value = [];
  dataList.value.forEach((item) => {
    applyIdList.value = [
      ...applyIdList.value,
      {
        id: item?.id,
        applyNo: item?.applyNo,
      },
    ];
    item.stepData = [];
    item.reserve = -1;

    // 预订按钮  鼠标移入
    item.schedule = false;
    // 更多按钮显示
    item.moreListShow = false;
    item.isMouseInter = false;

    item.tripList.forEach((trip, index) => {
      if (index == 0) {
        item.stepData = [
          ...item.stepData,
          {
            name: trip.beginCityName,
            timer: trip.beginDate,

            isTrain: trip.travelModeAggregate ? trip.travelModeAggregate.indexOf('火车') > -1 : false,
            isFly: trip.travelModeAggregate ? trip.travelModeAggregate.indexOf('飞机') > -1 : false,
            isHotal: trip.travelModeAggregate ? trip.travelModeAggregate.indexOf('酒店') > -1 : false,
          },
          {
            name: trip.endCityName,
            timer: trip.endDate,
            isTrain: trip.travelModeAggregate ? trip.travelModeAggregate.indexOf('火车') > -1 : false,
            isFly: trip.travelModeAggregate ? trip.travelModeAggregate.indexOf('飞机') > -1 : false,
            isHotal: trip.travelModeAggregate ? trip.travelModeAggregate.indexOf('酒店') > -1 : false,
          },
        ];
      } else {
        item.stepData = [
          ...item.stepData,
          {
            name: trip.endCityName,
            timer: trip.endDate,
            isTrain: trip.travelModeAggregate ? trip.travelModeAggregate.indexOf('火车') > -1 : false,
            isFly: trip.travelModeAggregate ? trip.travelModeAggregate.indexOf('飞机') > -1 : false,
            isHotal: trip.travelModeAggregate ? trip.travelModeAggregate.indexOf('酒店') > -1 : false,
          },
        ];
      }
    });
  });
  applyForm.value = dataList.value[0].id;
};

const goToDetail = (id: string) => {
  const url = tripUrl + '#' + '/detail?id=' + id;
  window.open(url);
};

// 表单验证状态
// 申请单单号
const showApplyNoHelp = ref(false);
const showDateHelp = ref(false);
const showAirPortHelp = ref(false)

const goToConfirm = (data:any) => {
  // 未推送成功不能确认
  if (data.shengyiOrderPushStatus != 20 && data.type != 1) {
    hMessage.error('订单推送商旅系统失败,请联系管理员后重试!')
    return
  }
  const url = businessTravel + '#' + '/confirmTrip?applyNo=' + data.applyNo;
  window.open(url);
}

const goToAudit = (code: string) => {
  if (code) {
    const url = processUrl + `?code=${code}#/details`;
    window.open(url);
  }
};

const goToList = (id: string) => {
  const url = indexUrl + '#' + '/card-order/trip';
  window.open(url);
};

const goToTeamList = (id: string) => {
  const url = indexUrl + '#' + '/card-order/team';
  window.open(url);
};

const goToApply = (id: string) => {
  const url = tripUrl + '#' + '/apply?id=' + id;
  window.open(url);
};

// 转换时间格式  2.1
const formateTime = (time: string) => {
  return ` ${new Date(time).getMonth() + 1}月${new Date(time).getDate()}日`;
};

const productNameFamatter = (value: string) => {
  switch (value) {
    case '飞机':
      return 'air';
    case '火车':
      return 'train';
    case '酒店':
      return 'hotel';
    case '地面服务':
      return 'groundServices';
    
    case '租车':
      return 'car';
      
    case '用车':
      return 'car';
    default:
      return 'air';
  }
}

watch(applyForm, (newValue) => {
  activeApply.value = dataList.value.filter((item) => item.id == newValue)[0];
  reserveList.value= []
  activeApply.value?.tripList?.forEach((trip, index) => {
    trip?.tripDetailMapList?.forEach((detail:any) => {
        reserveList.value = [...reserveList.value,
          {
            start: trip.beginCityName,
            beginCityCode: trip.beginCityCode,
            beginCityCodeSy: trip.beginCityCodeSy,
            endCityCode: trip.endCityCode,
            endCityCodeSy: trip.endCityCodeSy,
            startTime: trip.beginDate ,
            last: trip.endCityName,
            lastTime: trip.endDate,
            travelerList: detail.travelApplyTripDetailList,
            type: productNameFamatter(detail.productName)
          }
        ]
      });

})
});
// 预订下拉里的预订按钮样式（可以用hover实现，不清楚为啥）
const reserve = ref(-1);
// 预订移入
const handelReserve = (value: number) => {
  reserve.value = value;
};
// 预订移出
const handelReserveLeave = () => {
  reserve.value = -1;
};
// 预订列表
const reserveList = ref<any>([
 
]);
// 出差申请单  更多操作
let moreList = ref([
  {
    label: '作废',
    url: voidImg,
    urlAct: voidAct,
  },
  {
    label: '撤回',
    type: 'apply',
    url: withdraw,
    urlAct: withdrawAct,
  },
  // 变更单撤回
  {
    label: '撤回',
    type: 'bg',
    url: withdraw,
    urlAct: withdrawAct,
  },
  {
    label: '变更',
    url: change,
    urlAct: changeAct,
  },
]);

// 可报销的人员列表
const ReimburseTravelerList = ref<Array<any>>([])
// 选择的报销人
const reimburseUserCode = ref(null)
// 选中的申请单数据
const activeApplyData = ref<any>({})
const reimburseDialog = ref(false)

// 跳转去报销页面
const eesGingle = import.meta.env.VITE_BUSINESS_EES_SINGLE;

const eesUrl = ref('')
const showReimburseDialog = (data:any) => {
  activeApplyData.value = data

  // 如果是经办人可以给所有人报销
  // 否则只能给自己报销
  if(loginUser?.value?.username == data.operUserNo) {
    ReimburseTravelerList.value = data?.travelerList?.filter((item:any) => item.travelUserType == 0 && item.reimburseNum <= 1)
    if ( ReimburseTravelerList.value.length == 0) {
      hMessage.warning('暂无可报销人员!')
      return
    }
    reimburseUserCode.value = null
    reimburseDialog.value = true
  }else {
    reimburseUserCode.value = loginUser?.value?.username
    goToEES()
  }

}
const closeReimburseDialog = () => {
  reimburseUserCode.value = null
  reimburseDialog.value = false
  ReimburseTravelerList.value = []

}
const goToEES = () => {
  if(!reimburseUserCode.value) {
    hMessage.warning('请选择报销人员!')
    return
  }
  /*

  首先验证iamtoken是否过期
  1、如果过期需要用户去账号中心登录
   1.1、登录后重定向回来
   1.2、根据路由参数 isReimburse 判断是否要去往上一次点击的去报销字段
   1.3、
  2、如果没过期则直接跳转
  
  */ 
  const token = loginUser?.value?.extended?.iamToken

  loginApi.checkIamToken({token}).then(res => {
    console.log('验证iam_token是否有效----->>', res)
    if (res.data) {
      eesUrl.value = `${eesGingle}?travelCode=${activeApplyData.value.applyNo}&accountCode=${reimburseUserCode.value}&creatorCode=${loginUser.value?.username}&accessToken=${token}&fromOrigin=SLPC`
      // 清除上次本地缓存的数据
      console.log('去报销----->>', eesUrl.value)
      window.open(eesUrl.value)
      localStorage.removeItem('reimburseData')
      reimburseUserCode.value = null
      reimburseDialog.value = false
      ReimburseTravelerList.value = []
    }else {
      // 如果token过期
      hModal.info({
        title: '提示',
        okText: '确定',
        content: h('div', {}, [
          h('p', '当前集团统一认证系统已过期，请重新登录！'),
        ]),
        onOk() {
          console.log('ok');
          // 将这次跳转去报销的数据本地缓存起来
          localStorage.setItem('reimburseCode', JSON.stringify(reimburseUserCode.value))
          localStorage.setItem('reimburseData', JSON.stringify(activeApplyData.value))
          logout()
        },
      });

    }
  })

   
  
}

const logout = () => {
  loginApi.haierIamTokenLogout({ token: loginUser?.value?.extended?.iamToken }).finally(() => {
    removeStorageItem(HeaderConstant.TOKEN_KEY.key, false)
    window.location.reload()
  })
}

// 出差申请单中预订按钮
let schedule = ref(false);
// 鼠标移入
const handelSchedule = () => {
  schedule.value = true;
};
// 鼠标移出
const handelScheduleLeave = () => {
  schedule.value = false;
};
// 更多按钮显示
let moreListShow = ref(false);
const handelMoreMouse = () => {
  moreListShow.value = true;
  isMouseInter.value = true;
};
const handelMoreMouseLeave = () => {
  moreListShow.value = false;
  isMouseInter.value = false;
};
// 更多按钮鼠标移入
let moreActNumber = ref(-1);
const handelMoresMouse = (value: number) => {
  moreActNumber.value = value;
};
const handelMoresMouseLeave = () => {
  moreActNumber.value = -1;
};
// 机票下子标题
// 暂时隐藏国际/中国港澳台   '国际/中国港澳台',
const travelTypeList = ref(['国内机票','国际/中国港澳台', '地面服务', '团队票']);
// 本地跳转用于调试 跳转变更单 跳转详情
const teamDetail = import.meta.env.VITE_BUSINESS_TEAM_URL;


// 单点登录跳转第三方 -----------------------------
const TRIP_SINGLE = import.meta.env.VITE_BUSINESS_TRIP_SINGLE;

// 传入对象生成拼接字符串
const createUrlParams = (obj: { [key: string]: any }): string  => {
  const params = new URLSearchParams();
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      params.append(key, obj[key]?.toString());
    }
  }
  return params.toString();
}
const chosedPerson = ref<Array<any>>([])
// 选择出行人
const changeTicket = (vals: string[]) => {
  chosedPerson.value = []
  vals.forEach(item => {
    let person = {}
    person = creatTripParma.value.travelerList?.find(traveler => traveler.travelUserSyId == item)
    chosedPerson.value.push(person)
  })
}

// 选择国际出行人
const changeInTicket = (vals: string[]) => {
  chosedInternationalUserList.value = []
  vals.forEach(item => {
    let person = {}
    person = creatTripParma.value.travelerList?.find(traveler => traveler.travelUserSyId == item)
    chosedInternationalUserList.value.push(person)
  })
}

// 格式化出行人id部门id数据  格式：用户id:部门id
const formatPerson = (list?: any[]) => {
  return list?.map(item => `${item.travelUserNo || item.username || item.travelUserSyId }:${item.travelUserDeptId || item.departmentCode || ""}`)?.join(',')
}

// 地面服务查询
const terminalSearch = () => {

  if(!creatTripParma.value?.applyNo) {
    showApplyNoHelp.value = true
    return
  }

  if(!creatTripParma.value?.terminal) {
    showAirPortHelp.value = true
    return
  }

  if(!creatTripParma.value?.terminaDate) {
    showDateHelp.value = true
    return
  }

  showApplyNoHelp.value =false
  showDateHelp.value =false
  showAirPortHelp.value= false

  
  const params = {
    skipType: '010012',
    ccsqdh: applyNo.value || '',
    cxr: chosedPerson.value?.map(item => item.travelUserName).join(','), // 出行人
    cxr_hid: formatPerson(chosedPerson.value),  // 出行人id，格式：用户id:部门id
    sqdlx: 0,
    ygys: travelReason.value, // 因公因私 1因公 2因私
    ydsj: creatTripParma.value.terminaDate,
    fwzdmc: creatTripParma.value.terminalText,
    fwzdid: creatTripParma.value.citySyId,
    hzl: creatTripParma.value.terminal,
    fwcsid: creatTripParma.value.airportSyId,
    fwcsmc: creatTripParma.value.terminalText,
  }
  
  const url = TRIP_SINGLE + 'fcc/airservice/book/search.html?' + createUrlParams(params)
  console.log('链接---------', url)
  window.open(url,'_blank')
}

// 地面服务修改时间
const setTerminalDate = (val) => {
  creatTripParma.value.terminaDate = val
}



// 选择航站楼
const chosedTerminal = (city:any,airport:any,terminal:any) => {
  creatTripParma.value.terminalText = `${airport.name}${terminal.terminal}航站楼`
  // creatTripParma.value.citySyId = airport?.vetechAirportBh
  creatTripParma.value.citySyId = airport?.vetechAirportBh
  creatTripParma.value.terminal = terminal.terminal
  creatTripParma.value.airportSyId = city?.providerMapList[0]?.districtId || '';
}

// 国内机票查询 type 1 仅查询 2 搜索
const ticketSearch = async(type:number) => {

  if(!creatTripParma.value?.beginDate) {
    showDateHelp.value = true
    return
  }

  if(!creatTripParma.value?.endDate && travelType.value == 2) {
    showDateHelp.value = true
    return
  }

  if(!creatTripParma.value?.beginCityName || !creatTripParma.value?.endCityName) {
    showAirPortHelp.value = true
    return
  }
  showDateHelp.value =false
  showAirPortHelp.value= false

  if(!creatTripParma.value?.beginCityHid) {
    const beginAirport1 = await getAirportList(creatTripParma.value?.beginCityCode)
    creatTripParma.value['beginCityHid']= beginAirport1[0]?.threeCharacterCode
  }


  if(!creatTripParma.value?.endCityHid) {
    const endAirport = await getAirportList(creatTripParma.value?.endCityCodeFirst)
    creatTripParma.value['endCityHid'] = endAirport[0]?.threeCharacterCode
  }


  const params = {
    skipType: '010010',
    czly: travelReason.value == 1 ? type == 1 ? 'jcx' : 'sqd' : '',
    ygys: travelReason.value, // 因公因私 1因公 2因私
    ccsqdh: applyNo.value,
    xclx: travelType.value, // 行程类型 1单程 2往返 3多程
    sqdlx: 0, // 申请单类型  0为出差申请单
    cxr: travelReason.value == 1 ? chosedPerson.value?.map(item => item.travelUserName).join(',') : loginUser.value?.nickName, // 出行人
    cxr_hid: travelReason.value == 1 ? formatPerson(chosedPerson.value) : `${loginUser.value?.username}:${loginUser.value?.departmentCode}`,  // 出行人id，格式：用户id:部门id
    cfcs: creatTripParma.value?.beginCityName, 
    cfcs_hid: creatTripParma.value?.beginCityHid,
    cfcs_iscity: 0,
    cfrq: creatTripParma.value?.beginDate || dayjs().format('YYYY-MM-DD'),
    ddcs: creatTripParma.value?.endCityNameFirst,
    ddcs_hid: creatTripParma.value?.endCityHid,
    ddcs_iscity: 0,// 到达城市识别标识 识别城市还是机场 1城市 0机场
    ddrq: creatTripParma.value?.endDate,
    
    cxrlx: covertLx(chosedPerson.value) || 1, // 出行人类型 1内部员工 2外部人员
  }
  
  const url = TRIP_SINGLE + 'fcc/ticket/lticketbook/search/single/index.html?' + createUrlParams(params)
  console.log(url)
  window.open(url,'_blank')

}
const internationalCwdj = ref()


// 国际机票查询
const internationalTicketSearch = async() => {
  if(!creatTripParma.value?.applyNo && travelReason.value !=2) {
    showApplyNoHelp.value = true
    return
  }

  showApplyNoHelp.value =false
  showDateHelp.value =false
  showAirPortHelp.value= false

  if(!creatTripParma.value?.beginCityHid) {
    const beginAirport1 = await getAirportList(creatTripParma.value?.beginCityCode)
    creatTripParma.value['beginCityHid']= beginAirport1[0]?.threeCharacterCode
  }


  if(!creatTripParma.value?.endCityHid) {
    const endAirport = await getAirportList(creatTripParma.value?.endCityCodeFirst)
    creatTripParma.value['endCityHid'] = endAirport[0]?.threeCharacterCode
  }
  
  const params = {
    skipType: '010011',
    ygys: travelReason.value, // 因公因私 1因公 2因私
    cwdj: internationalCwdj.value || '', // 舱位等级 1代表经济舱，4代表超级经济舱，2代表公务舱，3代头等舱
    xclx: travelType.value, // 行程类型 1单程 2往返 3多程

    cxr: chosedInternationalUserList.value?.map(item => item.nickName || item.travelUserName).join(','), // 出行人
    cxr_hid: formatPerson(chosedInternationalUserList.value),  // 出行人id，格式：用户id:部门id
    
    cfcs: creatTripParma.value?.beginCityName || '', 
    cfcs_hid: creatTripParma.value?.beginCityHid || '',
    cfcs_iscity: 1,
    cfrq: creatTripParma.value?.beginDate || dayjs().format('YYYY-MM-DD'),
    
    ddcs: creatTripParma.value?.endCityNameFirst || '',
    ddcs_hid: creatTripParma.value?.endCityHid || '',
    ddcs_iscity: 1,// 到达城市识别标识 识别城市还是机场 1城市 0机场
    ddrq: creatTripParma.value?.endDate || dayjs().format('YYYY-MM-DD'),
    
    cxrlx: covertLx(chosedPerson.value), // 出行人类型 1内部员工 2外部人员
  }
  // 多程行程
  let travelList = []
  let travelListStr = ''

  if (travelType.value === 3) {
    multipleTrips.value?.forEach(item => {
      travelList.push(
        {
          "depCityMc": item.beginCityName,
          "depCity": item.beginCityCode,
          "arrCityMc": item.endCityNameFirst,
          "arrCity": item.endCityCodeFirst,
          "depDate": item.beginDate,
          "depIsCity": 1,
          "arrIsCity": 1
        }
      )

    });
    
    console.log('国际机票-------多程数据', travelList)
    travelListStr = encodeURIComponent(JSON.stringify(travelList))
    
  }
  // 如果是多程 链接拼接多程数据
  if(travelType.value === 3){
    const url = TRIP_SINGLE + 'fcc/ticket/iticket/search/search.html?' + createUrlParams(params) +  '&travelList=' + travelListStr
    console.log('链接---------', url)
    window.open(url,'_blank')
  } else {
    const url = TRIP_SINGLE + 'fcc/ticket/iticket/search/search.html?' + createUrlParams(params)
    console.log('链接---------', url)
    window.open(url,'_blank')
  }

}

// 国际机票新增询价单
const addInquirySheet = async() => {

  if(!creatTripParma.value?.beginCityHid && creatTripParma.value?.beginCityCode) {
    const beginAirport1 = await getAirportList(creatTripParma.value?.beginCityCode)
    creatTripParma.value['beginCityHid']= beginAirport1[0]?.threeCharacterCode
  }


  if(!creatTripParma.value?.endCityHid && creatTripParma.value?.endCityCode) {
    const endAirport = await getAirportList(creatTripParma.value?.endCityCode)
    creatTripParma.value['endCityHid'] = endAirport[0]?.threeCharacterCode
  }
  const params = {
    skipType: '010011',
    ygys: travelReason.value, // 因公因私 1因公 2因私
    cwdj: internationalCwdj.value || '', // 舱位等级 1代表经济舱，4代表超级经济舱，2代表公务舱，3代头等舱
    xclx: travelType.value, // 行程类型 1单程 2往返 3多程

    cxr: chosedInternationalUserList.value?.map(item => item.nickName || item.travelUserName).join(','), // 出行人
    cxr_hid: formatPerson(chosedInternationalUserList.value),  // 出行人id，格式：用户id:部门id
    
    cfcs: creatTripParma.value?.beginCityName || '', 
    cfcs_hid: creatTripParma.value?.beginCityHid || '',
    cfcs_iscity: 1,
    cfrq: creatTripParma.value?.beginDate || dayjs().format('YYYY-MM-DD'),
    
    ddcs: creatTripParma.value?.endCityName || '',
    ddcs_hid: creatTripParma.value?.endCityHid || '',
    ddcs_iscity: 1,// 到达城市识别标识 识别城市还是机场 1城市 0机场
    ddrq: creatTripParma.value?.endDate || dayjs().format('YYYY-MM-DD'),
    
    cxrlx: covertLx(chosedPerson.value), // 出行人类型 1内部员工 2外部人员
  }
  // 多程行程
  let travelList = []
  let travelListStr = ''

  if (travelType.value === 3) {
    multipleTrips.value?.forEach(item => {
      travelList.push(
        {
          "depCityMc": item.beginCityName,
          "depCity": item.beginCityCode,
          "arrCityMc": item.endCityName,
          "arrCity": item.endCityCode,
          "depDate": item.beginDate,
          "depIsCity": 1,
          "arrIsCity": 1
        }
      )

    });
    
    console.log('国际机票-------多程数据', travelList)
    travelListStr = encodeURIComponent(JSON.stringify(travelList))
    
  }
  // 如果是多程 链接拼接多程数据
  if(travelType.value === 3){
    const url = TRIP_SINGLE + 'fcc/ticket/iticket/inquiry/add.html?' + createUrlParams(params) +  '&travelList=' + travelListStr
    console.log('链接---------', url)
    window.open(url,'_blank')
  } else {
    const url = TRIP_SINGLE + 'fcc/ticket/iticket/inquiry/add.html?' + createUrlParams(params)
    console.log('链接---------', url)
    window.open(url,'_blank')
  }
}

// 国际查询询价单
const searchInquirySheet = async() => {
  const url = TRIP_SINGLE + 'fcc/ddgl/ddgl.html?cplx=jp&cpzl=xjd&skipType=1007'
  window.open(url,'_blank')
}


// ------酒店查询功能-----
// 酒店等级
const hotelLevel = ref('') 
const changeLevel = (val: string) => {
  hotelLevel.value = val
}
// 城市信息
const hotelChosedCityName = ref<string>()
const hotelChosedCityCode = ref<string>()

const chosedHotelCity = (city: CityItem) => {
  hotelChosedCityName.value = city.name;
  hotelChosedCityCode.value = city?.providerMapList[0]?.districtId || city?.syId;
}
// 入住日期
const hotelInDate = ref<string>()
const hotelLeaveDate = ref<string>()
const setHotelInDate = (date: string) => {
  hotelInDate.value = date
}
const setHotelLeaveDate = (date: string) => {
  hotelLeaveDate.value = date
}

// 关键词
const hotelKeyWord = ref<string>()
const hotelKeyWordChange = (val) => {
  console.log('关键词', val)
  hotelKeyWord.value=val
}

// 酒店查询type 1 仅查询 2 搜索
const hotelSearch  = async (type:number) => {
  if (!applyNo.value && type == 2 && travelReason.value == 1) {
    showApplyNoHelp.value = true
    return
  }else if(!hotelChosedCityCode.value) {
    showAirPortHelp.value = true
    return
  }else if (!hotelLeaveDate.value || !hotelInDate.value) {
    showDateHelp.value = true
    return
  }
  showApplyNoHelp.value = false
  showAirPortHelp.value = false
  showDateHelp.value = false

  const params = {
    skipType: '010013',
    onlySearch: type == 1 ? 1: 0,
    // czly: type == 1 ? 'jcx' : 'sqd',
    ygys: travelReason.value, // 因公因私 1因公 2因私
    ccsqdh: applyNo.value,
    // ifsqd: applyNo.value? 1 : 0,
    arrType: 4,
    xj: hotelLevel.value || '',
    gjc: hotelKeyWord.value || '',
    
    
    cxr: chosedPerson.value.map(item => item.travelUserName).join(','), // 出行人
    cxr_hid: formatPerson(chosedPerson.value),  // 出行人id，格式：用户id:部门id
    cxrlx: covertLx(chosedPerson.value), // 出行人类型 1内部员工 2外部人员
    cxrzj: await convertZJ(chosedPerson.value), //出行人职级

    cfcs: hotelChosedCityName.value, 
    cfcs_hid: hotelChosedCityCode.value,
    
    cfrq: hotelInDate.value || dayjs().format('YYYY-MM-DD'),
    ddrq: hotelLeaveDate.value|| dayjs().format('YYYY-MM-DD'),
    
  }
  const url = TRIP_SINGLE + 'fcc/hotel/list/list.html?' + createUrlParams(params)
  console.log('链接---------', url)
  window.open(url,'_blank')
}


// ------酒店查询功能-----



// 团队申请票相关---------------------------------------
const teamForm = ref<TCteateTeam>({
  beginCityCode: '',
  beginCityName: '',


  endCityCode: '',
  endCityName: '',


  // 出差类型0因公1因私
  evectionType: 0,

  // 附件地址
  travelerFileUrl: '',
  travelerFileName: '',

  // 产品类型
  destineInfoArr: [0],
  teamDestinePlaneTicket: {
    voyageType: 1,
  },
  teamDestineHotel: {},

  contactDeptName: loginUser.value?.departmentName || loginUser.value?.enterpriseName,
  contactUserCode: loginUser.value?.username, //联系人工号
  contactUserName: loginUser.value?.nickName, //联系人名称
  // 先不存手机号 因为加密未解析导致报错问题
  contactUserPhone: loginUser.value?.phone, //联系人电话
  contactUserMail: loginUser.value?.email, //联系人邮箱

})
// 保存并且跳转团队票申请页
const saveAndGoAddTeam = () => {
  
  if (teamForm.value.id) {
    teamApi.updateTeam(teamForm.value).then((res) => {
      const url = teamDetail + '#' + '/pc/addTeam?id=' + teamForm.value.id;
      window.open(url);
    });
  } else {
    teamApi.addTeam(teamForm.value).then((res) => {
      teamForm.value.id = res.id;
      const url = teamDetail + '#' + '/pc/addTeam?id=' + teamForm.value.id;
      window.open(url);
    });
  }
}

const chosedBeginCity = (city: CityItem) => {
  teamForm.value.beginCityCode = city.citycode;
  teamForm.value.beginCityName = city.name;
};
const chosedEndCity = (city: CityItem) => {
  teamForm.value.endCityCode = city.citycode;
  teamForm.value.endCityName = city.name;
};

// 选择起始站
const chosedBeginTrainStation = (item) => {
  creatTripParma.value.startTrainCode = item.stationCode
  creatTripParma.value.beginCityName = item.stationName
  creatTripParma.value.beginCityCode = String(item.cityId)

}
const chosedEndTrainStation = (item) => {
  creatTripParma.value.endTrainCode = item.stationCode
  creatTripParma.value.endCityName = item.stationName
  creatTripParma.value.endCityCode = String(item.cityId)
  creatTripParma.value.endCityNameFirst = item.stationName
  creatTripParma.value.endCityCodeFirst = ritem.stationCode
}

const setBeginTime = (time:string) => {
  teamForm.value.beginDate =time;
}
const setEndTime = (time:string) => {
  teamForm.value.endDate =time;
}

const goToAddTeam = () => {
  const url = teamDetail + '#' + '/pc/addTeam';
  window.open(url);
}

// 团队申请票相关---------------------------------------



let travelTypeAct = ref(0);
const handelticketsChange = (value: number) => {
  if(value == 3) {
    goToAddTeam()
    return
  }
  creatTripParma.value = {};
  planEndDate.value = '';
    planBeginDate.value = '';
  travelTypeAct.value = value;
  // 还原单程和因公
  travelType.value = 1;
  travelReason.value = 1;

  // 还原验证
  showApplyNoHelp.value =false
  showDateHelp.value =false
  showAirPortHelp.value= false
  applyId.value = '';
  applyNo.value = '';
};

// 酒店下子标题
const hotelTitleList = ref(['国内酒店', '团队票']);
let hotelTitleAct = ref(0)
const handelHotelTitleChange = (value: number) => {
  if(value != 0) {
    goToAddTeam()
    return
  }
  hotelTitleAct.value = value;
  // 还原单程和因公
  travelType.value = 1;
  travelReason.value = 1;
};

// 按钮鼠标移入
let isMouseInter = ref(false);

// 出差申请单按钮
const isApplyHover = ref(false);

//#endregion

//#region 机票

// 单程，返程
const travelType = ref(1);
// 因公因私
const travelReason = ref(1);

watch(travelReason, (newValue) => {
  creatTripParma.value = {}
  planEndDate.value = '';
  planBeginDate.value = '';
  hotelLeaveDate.value = ''
  hotelInDate.value = ''
})

const applyId = ref<string>('')
const applyNo = ref<string>('')
const creatTripParma = ref<ICreatTrip>({})
const changeApplyNo = (id: string, code: string) => {
  if(!id || !code) {
    creatTripParma.value = {}
    planEndDate.value = '';
    planBeginDate.value = '';
    hotelLeaveDate.value = ''
    hotelInDate.value = ''
    hotelChosedCityName.value = ''
    hotelChosedCityCode.value = ''
  }
  applyId.value = id
  applyNo.value = code
}
const planBeginDate = ref('')
const planEndDate = ref('')

const getApplyDetail = () => {
  tripApi.queryDetailByApplyNo(applyId.value).then((res:any) => {
    // 添加第一行程的终点数据,用于页面展示
    res.endCityNameFirst = res.tripList[0]?.endCityName
    res.endCityCodeFirst = res.tripList[0]?.endCityCode
    res.endCityCodeSyFirst = res.tripList[0]?.endCityCodeSy


    creatTripParma.value = res
    chosedPerson.value = []
    hotelChosedCityName.value = res.endCityName
    hotelChosedCityCode.value = res.endCityCodeSy

    hotelLeaveDate.value = res.endDate
    hotelInDate.value = res.beginDate

    //
    planBeginDate.value = res.beginDate
    planEndDate.value = res.endDate

  })
}
watch(
  () => applyId.value,
  (newVal, oldVal) => {
    if (newVal) {
      // 根据id获取申请单详情信息
      getApplyDetail()
    }
  }
)

const setBeginTimeTicket = (time:string) => {
  creatTripParma.value.beginDate =time
}
const setEndTimeTicket = (time:string) => {
  creatTripParma.value.endDate =time
}

const chosedBeginCityTicket = (city: CityItem) => {
  creatTripParma.value.beginCityCode = String(city.cityId);
  creatTripParma.value.beginCityHid = city.threeCharacterCode;
  creatTripParma.value.beginCityName = city.name;
};
const chosedEndCityTicket = (city: CityItem) => {
  // creatTripParma.value.endCityCode = city.cityId;

  creatTripParma.value.endCityHid = city.threeCharacterCode;
  creatTripParma.value.endCityName = city.name;
  creatTripParma.value.endCityCodeFirst = String(city.cityId);
  creatTripParma.value.endCityNameFirst = city.name;
};
// 火车选择城市
const chosedBeginCityTrain = (city: CityItem) => {
  creatTripParma.value.beginCityCode = city.citycode;
  creatTripParma.value.beginCityName = city.name;
}
const chosedEndCityTrain = (city: CityItem) => {
  creatTripParma.value.endCityCode = city.citycode;
  creatTripParma.value.endCityName = city.name;

  creatTripParma.value.endCityCodeFirst = city.citycode;
  creatTripParma.value.endCityNameFirst = city.name;
}

// 火车修改时间
const setBeginTimeTrain = (val:any) => {
  creatTripParma.value.beginDate = val
}

// 火车仅查询 根据城市id获取火车三字码
const trainSearch = async(type:number) => {

  if(!creatTripParma.value?.applyNo && type ==2) {
    showApplyNoHelp.value = true
    return
  }

  if(!creatTripParma.value.beginCityCode || !creatTripParma.value.endCityCode) {
    showAirPortHelp.value = true
    return
  }

  if(!creatTripParma.value.beginDate) {
    showDateHelp.value = true
    return
  }

  showApplyNoHelp.value =false
  showDateHelp.value =false
  showAirPortHelp.value= false


  let params = {}
  let url = ''
  const beginTrain = await getTrainStationByCityId(creatTripParma.value.beginCityCode)
  const endTrain = await getTrainStationByCityId(creatTripParma.value.endCityCodeFirst)
    if (beginTrain[0].threeCharacterCode && endTrain[0].threeCharacterCode) {
      params = {
        skipType: '010014',
        cfcs: creatTripParma.value?.beginCityName,
        cfcs_hid: creatTripParma.value?.startTrainCode || beginTrain[0]?.threeCharacterCode,
        ddcs: creatTripParma.value?.endCityNameFirst,
        ddcs_hid:creatTripParma.value?.endTrainCode || endTrain[0]?.threeCharacterCode,

        cxr: chosedPerson.value?.map(item => item.travelUserName).join(','), // 出行人
        cxr_hid: formatPerson(chosedPerson.value),  // 出行人id，格式：用户id:部门id

        ccsqdh: creatTripParma.value?.applyNo || '',
        // ccsqdcx: 1,

        cfrq: creatTripParma.value?.beginDate,
        ygys: 1,
        xclx: 1,
        sfkqjcx: type == 1 ? 1 :''
      }
      url = TRIP_SINGLE + 'view/fcc/train/book/search.html?' + createUrlParams(params)

    }else {
      hMessage.error('出发城市或到达城市没有火车站,请重新选择!')
      return
    }
  window.open(url)
}

watch(travelType, (newValue) => {
  showApplyNoHelp.value =false
  showDateHelp.value =false
  showAirPortHelp.value= false

  if (newValue === 3) {
    multipleTrips.value.push({
      key: guid(),
      beginCityName: '',
      beginCityCode:'',
      endCityName: '',
      endCityCode: '',
      
      beginDate: '',
      canDelete: false,
      depCityMc: '',
      depCity: '',
      arrCityMc: '',
      arrCity: '',
      depDate: '',
    });
  } else {
    multipleTrips.value = [
      {
        key: guid(),
        beginCityName: '',
        beginCityCode:'',

        endCityName: '',
        endCityCode: '',
        beginDate: '',
        canDelete: false,
        depCityMc: '',
        depCity: '',
        arrCityMc: '',
        arrCity: '',
        depDate: '',
      },
    ];
  }
  
});
const businessTravel = import.meta.env.VITE_BUSINESS_TRIP_URL;

const clickItem = (item: any, id: string, applyNo: string) => {
  if (item.label == '变更') {
    goToChange(applyNo);
  } else if (item.label == '作废') {
    Modal.confirm({
      title: '提示',
      icon: createVNode(ExclamationCircleOutlined),
      content: createVNode('div', { style: 'color:red;' }, '确定要作废这条数据吗?'),
      onOk() {
        cancelApply(id);
      },
      onCancel() {
        console.log('Cancel');
      },
      class: 'test',
    });
  } else if (item.label == '撤回') {
    if(item.type == 'bg') {
      recallBgApply(applyNo);
    }else {
      recallApply(id);
    }
  }
};


// 根据城市id获取机场数据
const getAirportList =  async(cityIds?: string) => {
  const res = await cityApi.getAirportByCityId({ cityIds })
  return res
}
// 根据城市id获取车站数据
const getTrainStationByCityId =  async(cityIds: string) => {
  const res = await cityApi.getTrainStationByCityId({ cityIds })
  return res
}

// 根据城市id获取航站楼数据
const getAirportList2 =  async (cityIds: string) => {
  const res = await cityApi.getAirportByCityId({ cityIds, isNeedAirportTerminal: true,domesticInternationalType: 1  })
  return res
}

// 判断申请日期是否早于当天,如果早于当天返回当天日期
const compareDate = (date: string) => {
  if (dayjs(date).isBefore(dayjs(), 'day')) {
    return dayjs().format('YYYY-MM-DD')
  }
  return date
}

// 根据类型跳转预订页
const reserveByType = async(trip:any,item:any) => {
  // 未推送成功 或者 不是从胜意拉取的数据不能确认
  if (trip.shengyiOrderPushStatus != 20 && trip.type != 1 ) {
    hMessage.error('订单推送商旅系统失败,请联系管理员后重试!')
    return
  }
  let params = {}
  let url = ''
  if(item.type =='air') {
    // 1、飞机首先根据城市 id获取 出发机场、到达机场编号
    const beginAirport = await getAirportList(item.beginCityCode)
    const endAirport = await getAirportList(item.endCityCode)
    if (beginAirport[0].threeCharacterCode && endAirport[0].threeCharacterCode) {
      params = {
        skipType: '010010',
        // czly: 'jcx',
        ygys:  trip?.travelReserveFlag ? 1 : 2, // 因公因私 1因公 2因私
        ccsqdh:  trip?.applyNo || '',
        xclx: 1, // 行程类型 1单程 2往返 3多程
        sqdlx: 0, // 申请单类型  0为出差申请单
        cxr: item?.travelerList?.map(item => item.travelUserName).join(','), // 出行人
        cxr_hid: formatPerson(item?.travelerList),  // 出行人id，格式：用户id:部门id
        cfcs:  item?.start, 
        cfcs_hid:  beginAirport[0].threeCharacterCode,
        cfcs_iscity: 1,
        cfrq: compareDate(item?.startTime),
        ddcs: item?.last,
        ddcs_hid:  endAirport[0].threeCharacterCode,
        ddcs_iscity: 1,// 到达城市识别标识 识别城市还是机场 1城市 0机场
        ddrq:  item?.lastTime,
        cxrlx: covertLx(item?.travelerList), // 出行人类型 1内部员工 2外部人员
      }
      url = TRIP_SINGLE + 'fcc/ticket/lticketbook/search/single/index.html?' + createUrlParams(params)


    }else { 
      hMessage.error('出发城市或到达城市没有机场,请重新选择!')
      return
    }

  }else if(item.type == 'train'){
    // 2、火车首先根据城市 id获取 出发、到达站点编号
    const beginTrain = await getTrainStationByCityId(item.beginCityCode)
    const endTrain = await getTrainStationByCityId(item.endCityCode)
    if (beginTrain[0].threeCharacterCode && endTrain[0].threeCharacterCode) {
      params = {
        skipType: '010014',
        cxr: item?.travelerList?.map(item => item.travelUserName).join(','), // 出行人
        cxr_hid: formatPerson(item?.travelerList),  // 出行人id，格式：用户id:部门id
        cxrlx: covertLx(item?.travelerList), // 出行人类型 1内部员工 2外部人员
        ccsqdh:  trip?.applyNo,
        // ccsqdcx: 1,
        cfcs: item?.start,
        cfcs_hid: beginTrain[0]?.threeCharacterCode,
        ddcs: item?.last,
        ddcs_hid: endTrain[0]?.threeCharacterCode,
        cfrq: compareDate(item?.startTime),
        ygys: trip?.travelReserveFlag ? 1 : 2,
        xclx: 1,
        // sfkqjcx: 1
      }
      url = TRIP_SINGLE + 'view/fcc/train/book/search.html?' + createUrlParams(params)

    }else {
      hMessage.error('出发城市或到达城市没有火车站,请重新选择!')
      return
    }

  }else if(item.type=='hotel') {
    params = {
      skipType: '010013',
      onlySearch:0,
      // czly: type == 1 ? 'jcx' : 'sqd',
      ygys: trip?.travelReserveFlag ? 1 : 2, // 因公因私 1因公 2因私
      ccsqdh:trip?.applyNo || '',
      // ifsqd: 1,
      arrType: 4,
      gjc:'',
      cxr: item?.travelerList?.map(person => person.travelUserName).join(','), // 出行人
      cxr_hid: formatPerson(item?.travelerList),   // 出行人id，格式：用户id:部门id
      cxrlx: covertLx(item?.travelerList), // 出行人类型 1内部员工 2外部人员
      cxrzj: await convertZJ(item?.travelerList), //出行人职级

      cfcs: item?.last, 
      cfcs_hid: item?.endCityCodeSy,
      
      cfrq: compareDate(item?.startTime),
      ddrq: item?.lastTime,
      
    }
    url = TRIP_SINGLE + 'fcc/hotel/list/list.html?' + createUrlParams(params)
  }else if (item.type == 'groundServices') {
    // 根据城市查询航站楼信息
    const endCityHzl = await getAirportList2(item.endCityCode)

    params = {
      skipType: '010012',
      ccsqdh:trip?.applyNo,
      cxr: trip?.travelerList?.map(person => person.travelUserName).join(','), // 出行人
      cxr_hid: formatPerson(trip?.travelerList),   // 出行人id，格式：用户id:部门id
      sqdlx: 0,
      ygys: trip?.travelReserveFlag ? 1 : 2, // 因公因私 1因公 2因私
      ydsj: item?.lastTime,
      fwzdmc: `${endCityHzl[0].airportList[0].name}${endCityHzl[0].airportList[0].airportTerminalList[0].terminal}航站楼`,
      fwzdid: endCityHzl[0].airportList[0].vetechAirportBh,
      hzl: endCityHzl[0].airportList[0].airportTerminalList[0].terminal,
      fwcsid: item?.endCityCodeSy,
      fwcsmc: `${endCityHzl[0].airportList[0].name}${endCityHzl[0].airportList[0].airportTerminalList[0].terminal}航站楼`,
    }
    url = TRIP_SINGLE + 'fcc/airservice/book/search.html?' + createUrlParams(params)

  }

  window.open(url,'_blank')
}

const goToChange = (id: string) => {
  const url = businessTravel + '#' + '/update?applyNo=' + id;
  window.open(url);
};

const cancelApply = (id: any) => {
  tripApi.cancelApply({ id }).then((res) => {
    onFilterChange();
    message.success('作废成功!');
  })
};
const recallApply = (id: any) => {
  
  hModal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', {  }, '撤回后再提交,所有审批节点需重审,请确认是否继续进行此操作!'),
    onOk() {
      tripApi.recallApply({ id }).then((res) => {
        hMessage.success('撤回成功!');
        onFilterChange();
      });
    },
    onCancel() {
      console.log('取消');
    },
    class: 'test',
  });
};

const recallBgApply = (applyNo: any) => {
  hModal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', {  }, '撤回后再提交,所有审批节点需重审,请确认是否继续进行此操作!'),
    onOk() {
      tripApi.recallBgApply(applyNo).then((res) => {
        hMessage.success('撤回成功!');
        onFilterChange();
      });
    },
    onCancel() {
      console.log('取消');
    },
    class: 'test',
  });

};

const travelTypeChange = (num: number) => {
  travelType.value = num;
};

//#region 临时创建

type InternationalType = {
  beginCityName: string;
  beginCityCode: string;
  endCityName: string;
  endCityCode: string;
  beginDate:string;
  canDelete: boolean;
  key: string;
  depCityMc?: string;
  depCity?: string;
  
  arrCityMc?: string;
  arrCity?: string;
  depDate?: string;
  depIsCity?: number;
  arrIsCity?: number;
};

//#endregion



// 国际多程数组
const multipleTrips = ref<Array<InternationalType>>([
  {
    key: guid(),
    beginCityName: '',
    beginCityCode:'',
    depCityMc: '',
    depCity: '',
    arrCityMc: '',
    arrCity: '',
    depDate: '',

    endCityName: '',
    endCityCode: '',
    beginDate: '',
    canDelete: false,
  },
]);

const addTrip = () => {
  multipleTrips.value.push({
    key: guid(),
    beginCityName: '',
    beginCityCode:'',
    endCityName: '',
    endCityCode: '',
    beginDate: '',
    canDelete: true,
    depCityMc: '',
    depCity: '',
    arrCityMc: '',
    arrCity: '',
    depDate: '',
  });
};

const chosedInternationalUserList = ref<Array<IUserInfo>>([])

const changeInternationalUser = (list: Array<IUserInfo>) => {
  console.log('9999国际多选', list)
  chosedInternationalUserList.value = list
}

const chosedBeginCityInternational = (city: CityItem, item:InternationalType) => {
  item.beginCityCode = city.threeCharacterCode;
  item.beginCityName = city.name;
}
const chosedEndCityInternational = (city: CityItem, item:InternationalType) => {
  // item.endCityCode = city.threeCharacterCode;
  item.endCityName = city.name;
  item.endCityCodeFirst = city.threeCharacterCode;
  item.endCityNameFirst = city.name;
}

const setBeginTimeInternational = (date: string, item:InternationalType) => {
  item.beginDate = date
}

const deleteTrip = (key: string) => {
  multipleTrips.value = multipleTrips.value.filter((o) => o.key != key);
};

//#endregion

// #region 兼容老版本
const isBannerHover = ref(false)
const setIsBannerHover = (value: boolean) => {
  isBannerHover.value = value
}

const isLifeHover = ref(false)

const setIsLifeHover = (value: boolean) => {
  isLifeHover.value = value
}
const gotoUrl = (url: string | undefined) => {
  if(!url){
    message.error('链接错误！')
    return
  }
  const thisUrl = currentRouter.value.resolve({
    path: url,
  })
  window.open(thisUrl.href, '_blank')
  // router.push({ path: url })
}

//#endregion

</script>

<template>
  <div class="container">
    <div class="row relative" v-if="showOldIndex">
      <div class="banner">
        <div class="banner-list">
          <div class="bannet-list-top">
            <div
              v-for="(item, index) in bannerList"
              :key="index"
              class="banner-list-item"
              @mouseenter="handelBanMouse(index)"
              :class="{ bannerMouse: mouseEnter == index, bannerActive: activeBaner == index }"
              @click="handelBan(index)"
              @mouseleave="handelBanMouseLeave"
            >
              <img :src="item.url" alt="" class="banner-img-item" :class="'banner-img-item' + index" />
              <span class="banner-item-title">{{ item.title }}</span>
            </div>
          </div>
          <!-- 出差申请单卡片 -->
          <div class="banner-contain" v-if="activeBaner == 0">
            <div class="ban-con-title">
              <div class="ban-title-left">
                <div class="ban-border"></div>
                <span class="ttile">出差申请单</span>
              </div>
              <div class="ban-title-right pointer" @click="goToList(activeApply?.id)">
                <span>单据中心</span>
                <img src="../../assets/image/banner/right.png" alt="" class="" />
              </div>
            </div>

            <div v-if="dataList && dataList.length > 0" class="banner-contain-bottom">
              <div class="banner-contain-data">
                <div class="data-one">
                  <a-select
                    ref="select"
                    v-model:value="applyForm"
                    showSearch
                    optionFilterProp="applyNo"
                    :options="applyIdList"
                    :fieldNames="{ label: 'applyNo', value: 'id' }"
                  >
                  </a-select>
                </div>
                <div class="data-time " v-if="activeApply.beginDate && activeApply.endDate">
                  <img src="../../assets/image/banner/ban-time.png" alt="" />
                  <div class="peice-mar">{{ activeApply.beginDate }} ~ {{ activeApply.endDate }}</div>
                </div>
                <div class="data-price data-time">
                  <img src="../../assets/image/banner/money.png" alt="" />
                  <div class="peice-mar">
                    <span>{{ `¥ ${activeApply?.amountSum || 0}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') }}</span>
                    <span style="color: #3983e5">(总)</span>
                  </div>
                  <!-- <div class="peice-mar">
                    <span>{{ `¥ ${activeApply?.amountSum || 0}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') }}</span>
                    <span style="color: #52c41a">(可用)</span>
                  </div> -->
                </div>
                <div class="data-btn">

                  <!-- 审批状态 -->
                  <a-tooltip>
                    <template #title>审批状态</template>
                    <h-tag
                      v-if="activeApply.status != '90' && activeApply?.type != 1 && activeApply?.travelReserveFlag != 0"
                      :color="TripApprovalStatusToTagColor[activeApply?.auditStatus] || 'blue'"
                      class="pointer"
                      @click="goToAudit(activeApply?.workFlowId)"
                    >
                      <span>{{ TripApprovalStatus[activeApply?.auditStatus] || '' }} <QuestionCircleOutlined /></span>
                    </h-tag>
                  </a-tooltip>
                  <!-- 单据状态 -->
                  <a-tooltip>
                    <template #title>单据状态</template>
                    <h-tag :color="TripDocumentStatusToTagColor[activeApply?.status] || 'blue'">
                      <span>{{ TripDocumentStatus[activeApply?.status] || '' }} <QuestionCircleOutlined /></span>
                    </h-tag>
                  </a-tooltip>
                </div>
              </div>
              <div class="banner-detail">
                <!-- 个人信息 -->
                <div>
                  <div class="detail-info-i">
                    <div class="info-i-l">
                      <img src="../../assets/image/banner/person.png" alt="" />
                      <span style="padding-left: 5px">经办人</span>
                    </div>
                    <div class="info-main">
                      <a-tooltip>
                        <template #title>{{ activeApply?.operUserName }}</template>
                        <div class="names">{{ activeApply?.operUserName }}</div>
                      </a-tooltip>
                    </div>
                  </div>
                  <div class="detail-info-i">
                    <div class="info-i-l">
                      <img src="../../assets/image/banner/person.png" alt="" />
                      <span style="padding-left: 5px">出差人</span>
                    </div>
                    <div class="info-main">
                      <a-tooltip>
                        <template #title>{{ getMainPerson(activeApply?.travelerList) }}</template>
                        <div class="names">{{ getMainPerson(activeApply?.travelerList) }}</div>
                      </a-tooltip>
                    </div>
                  </div>
                  <div class="detail-info-i">
                    <div class="info-i-l">
                      <img src="../../assets/image/banner/friend.png" alt="" />
                      <span style="padding-left: 5px">同行人</span>
                    </div>
                    <div class="info-main">
                      <a-tooltip>
                        <template #title>{{ getOutPerson(activeApply?.travelerList) }}</template>
                        <div class="names">{{ getOutPerson(activeApply?.travelerList) }}</div>
                      </a-tooltip>
                    </div>
                  </div>
                  <!-- <div class="detail-info-i">
                    <div class="info-i-l">
                      <img src="../../assets/image/banner/reason.png" alt="" />
                      <span style="padding-left: 5px">出差事由</span>
                    </div>
                    <div class="info-main">
                      <a-tooltip>
                        <template #title>{{ activeApply?.travelReason }}</template>
                        <div class="names">{{ activeApply?.travelReason }}</div>
                      </a-tooltip>
                    </div>
                  </div> -->
                </div>
                <!-- 行程 -->
                <div class="banner-step" style="overflow: auto">
                  <div
                    v-for="(item, index) in activeApply?.stepData"
                    :key="index"
                    class="step-item"
                    :class="index == 0 ? 'step-item-start' : ''"
                  >
                    <div class="idol" v-if="index != 0">
                      <div class="idol-img">
                        <img src="../../assets/image/banner/icon-air.png" alt="" v-if="item.isFly" />
                        <img src="../../assets/image/banner/icon-train.png" alt="" v-if="item.isTrain" />
                      </div>
                    </div>
                    <div class="step-name">
                      <span class="step-c">
                        <a-tooltip>
                          <template #title>{{ item.name }}</template>
                          <span class="city-name">{{ item.name }}</span>
                        </a-tooltip>

                        <img
                          class="hotal-img"
                          src="../../assets/image/banner/yding-hotel.png"
                          alt=""
                          v-if="item.isHotal"
                      /></span>
                      <span class="step-t">{{ item.timer }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="btn-contain">

                <a-popover >
                  <template #content>
                    <a-row style="width: 200px; margin-bottom: 5px;" v-for="item, index in activeApply.travelerList" :key="index" v-show="item.travelUserType =='0'">
                      <template v-if="item.travelUserType =='0'">
                        <a-col :span="16">{{ `${item.travelUserName}(${item.travelUserNo })` }}</a-col>
                        <a-col :span="8">
                          <a-tag color="green" v-if="item.reimburseNum != 0">已报销</a-tag>
                          <a-tag color="orange" v-else>未报销</a-tag>
                        </a-col>
                      </template>
                    </a-row>
                  </template>
                  <!-- 报销 -->
                  <div class="btn" v-if="activeApply?.reimbursementButton" @click="showReimburseDialog(activeApply)">
                    <img src="../../assets/image/banner/baoxiao.png" alt="" />
                    <span>去报销</span>
                  </div>
                </a-popover>


                <div
                  class="btn btn-yu"
                  v-if="activeApply?.status == '30' && activeApply.travelReserveFlag == 1 && reserveList.length > 0"
                  @mouseenter="handelSchedule"
                  @mouseleave="handelScheduleLeave"
                >
                  <img src="../../assets/image/banner/yuding.png" alt="" />
                  <span>预订</span>
                  <div class="card-list" v-show="schedule">
                    <div class="triangle-con">
                      <img src="../../assets/image/banner/triangle.png" alt="" class="triangle" />
                    </div>
                    <template v-for="(item, index) in reserveList" >
                    <div class="card-list-item" :key="index" v-if="item.type != 'car'">
                      <img src="../../assets/image/banner/yding-air.png" alt="" class="item-l"  v-if="item.type == 'air'" />
                      <img src="../../assets/image/banner/yding-hotel.png" alt="" class="item-l"  v-if="item.type == 'hotel'" />
                      <img src="../../assets/image/banner/yding-train.png" alt="" class="item-l"  v-if="item.type == 'train'" />
                      <img src="../../assets/image/banner/groundServices1.png" alt="" class="item-l"  v-if="item.type == 'groundServices'" />
                      <div class="banner-step">
                        <div class="step-name_">
                          <a-tooltip>
                          <template #title>{{item.start }}</template>
                          <span>{{ item.start }}</span>
                        </a-tooltip>
                          <span class="step-t">{{ formateTime(item.startTime) }}</span>
                        </div>
                        <div class="idol_"></div>
                        <div class="step-name_">
                          <a-tooltip>
                          <template #title>{{item.last }}</template>
                          <span>{{ item.last }}</span>
                        </a-tooltip>
                          <span class="step-t">{{ formateTime(item.lastTime) }}</span>
                        </div>
                      </div>
                      <div
                        :class="reserve == index ? 'btn-reserve' : ''"
                        class="btn-step_"
                        @mouseenter="handelReserve(index)"
                        @mouseleave="handelReserveLeave"
                        @click="reserveByType(activeApply,item)"

                      >
                        预订
                      </div>
                    </div>
                  </template>
                  </div>
                </div>
                <div class="btn btn-yu btn-et" v-if="activeApply?.status == '10'&& activeApply?.status != '90' && loginUser?.username == activeApply?.createBy" @click="goToApply(activeApply?.id)">
                  <img src="../../assets/image/banner/btn-det.png" alt="" />
                  <span>编辑</span>
                </div>
                <div class="btn btn-yu btn-et" v-else @click="goToDetail(activeApply?.id)">
                  <img src="../../assets/image/banner/btn-det.png" alt="" />
                  <span>详情</span>
                </div>

                <a-popover >
                  <template #content>
                    <a-row style="width: 200px; margin-bottom: 5px;" v-for="item, index in activeApply.travelerList" :key="index" v-show="item.travelUserType =='0'">
                      <template v-if="item.travelUserType =='0'">
                        <a-col :span="16">{{ `${item.travelUserName}(${item.travelUserNo })` }}</a-col>
                        <a-col :span="8">
                          <a-tag color="orange" v-if="item.reimburFinish == '10'">未确认</a-tag>
                          <a-tag color="green" v-else-if="item.reimburFinish == '20'">已确认</a-tag>
                          <a-tag color="blue" v-else>系统确认</a-tag>
                        </a-col>
                      </template>
                    </a-row>
                  </template>
                  <!-- 行程确认 -->
                  <div class="btn btn-yu btn-yu-max btn-et" v-if="!activeApply.changeApplyStatus ? activeApply.confirmButton : (activeApply.confirmButton && activeApply.changeApplyStatus == '30')" @click="goToConfirm(activeApply)">
                    <img src="../../assets/image/banner/btn-det.png" alt="" />
                    <span>行程确认</span>
                  </div>
                  
                </a-popover>

                

                <div class="btn btn-yu btn-et" v-if="showMoreBtn(activeApply)" @mouseenter="handelMoreMouse" @mouseleave="handelMoreMouseLeave">
                  <img src="../../assets/image/banner/more.png" alt="" />
                  <span>更多</span>
                  <div class="card-more-ban" v-if="moreListShow">
                    <div class="triangle-con">
                      <img src="../../assets/image/banner/triangle.png" alt="" class="triangle" />
                    </div>

                    <div
                      v-for="(item, index) in moreList"
                      :key="index"
                      @mouseenter="handelMoresMouse(index)"
                      @mouseleave="handelMoresMouseLeave"
                      @click="clickItem(item, activeApply?.id, activeApply?.applyNo)"
                    >
                      <template
                        v-if="
                          item.label == '作废' &&
                          (activeApply?.status == '10' || activeApply?.status == '20' || activeApply?.status == '30') && activeApply.changeApplyStatus != '20' &&  activeApply.auditStatus!='20' && loginUser?.username == activeApply.createBy
                        "
                      >
                        <div class="more-ban-items">
                          <img :src="moreActNumber == index ? item.urlAct : item.url" alt="" />
                          <span>{{ item.label }}</span>
                        </div>
                      </template>
                      <template
                        v-else-if="
                          item.label == '撤回' && item.type=='apply' && activeApply?.status == '20' && activeApply?.auditStatus == '20'
                        "
                      >
                        <div class="more-ban-items">
                          <img :src="moreActNumber == index ? item.urlAct : item.url" alt="" />
                          <span>{{ item.label }}</span>
                        </div>
                      </template>

                      <!-- 变更单撤回 -->
                      <template
                        v-else-if="
                          item.label == '撤回' && item.type=='bg' && activeApply?.changeApplyStatus == '20'
                        "
                      >
                        <div class="more-ban-items">
                          <img :src="moreActNumber == index ? item.urlAct : item.url" alt="" />
                          <span>{{ item.label }}</span>
                        </div>
                      </template>

                      <template v-else-if="item.label == '变更' && activeApply?.status == '30'">
                        <div class="more-ban-items">
                          <img :src="moreActNumber == index ? item.urlAct : item.url" alt="" />
                          <span>{{ item.label }}</span>
                        </div>
                      </template>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 数据空状态 -->
            <div v-else class="banner-contain-empty banner-contain-bottom">
              <div class="banner-contain-empty-text">暂无出差申请单,若需要请按以下流程创建~</div>
              <div class="banner-contain-step-image">
                <img src="../../assets/image/banner/step.png" alt="" />
              </div>
            </div>

            <div
              class="add-apply pointer"
              @click="openNewWindow(tripUrl + '#/apply')"
              @mousemove="isApplyHover = true"
              @mouseleave="isApplyHover = false"
            >
              <img src="../../assets/image/banner/add-bot.png" alt="" />
              <span>创建新出差申请</span>
            </div>
          </div>
          <!-- 机票 -->
          <div
            class="banner-contain-tickets"
            v-if="activeBaner == 1"
            :class="
              travelTypeAct == 0
                ? 'banner-contain-tickets-o'
                : travelTypeAct == 1
                ? 'banner-contain-tickets-t'
                : 'banner-contain-tickets-th'
            "
          >
            <img
              src="../../assets/image/banner/travel-type-one.png"
              class="tickets-bg"
              alt=""
              v-show="travelTypeAct == 0"
            />
            <img
              src="../../assets/image/banner/travel-type-two.png"
              class="tickets-bg"
              alt=""
              v-show="travelTypeAct == 1"
            />
            <img
              src="../../assets/image/banner/travel-type-three.png"
              class="tickets-bg"
              alt=""
              v-show="travelTypeAct == 2"
            />
            
            <div class="contain-tickets-title">
              <div
                v-for="(item, index) in travelTypeList"
                :key="index"
                class="tickets-title-item"
                :class="travelTypeAct == index ? 'tickets-title-item-act' : ''"
                @click="handelticketsChange(index)"
              >
                {{ item }}
                <div class="border-t" v-if="travelTypeAct == index"></div>
              </div>
            </div>
            <!-- 国内机票 -->
            <div class="contain-tickets-form" v-if="travelTypeAct == 0">
              <div class="ticket-form-top">
                <div>
                  <a-radio-group v-model:value="travelType">
                    <a-radio :value="1">单程</a-radio>
                    <a-radio :value="2">返程</a-radio>
                  </a-radio-group>
                </div>
                <div class="travel-reason">
                  <div class="bgc-rea" :class="travelReason == 1 ? '' : 'bgc-rea-o'"></div>
                  <div
                    class="travel-reason-o"
                    @click="travelReason = 1"
                    :class="travelReason == 1 ? 'travel-reason-oa' : ''"
                  >
                    因公
                  </div>
                  <div
                    class="travel-reason-t"
                    @click="travelReason = 2"
                    :class="travelReason == 2 ? 'travel-reason-oa' : ''"
                  >
                    因私
                  </div>
                </div>
              </div>
              <div class="ticket-form-bottom">

                <a-form
                  :model="creatTripParma"
                  autocomplete="off"
                  layout="inline"
                  class="inline-form"
                >
                  <a-form-item
                      label=""
                      name="applyNo"
                      :validate-status="showApplyNoHelp ? 'error' : 'success'"
                      :has-feedback="showApplyNoHelp ? true : false"
                      :help="showApplyNoHelp ? '请先选择申请单': ''"
                      v-if="travelReason === 1" 
                    >
                    <apply-no-com key="gnjp" :travelType="travelType" @change="changeApplyNo" />
                  </a-form-item>

                  <a-form-item
                      label=""
                      v-if="travelReason === 1" 
                    >
                    <traveler-select :travelType="travelType" @change="changeTicket" :list="creatTripParma?.travelerList"  />
                  </a-form-item>

                  <a-form-item
                      label=""
                      :validate-status="showAirPortHelp ? 'error' : 'success'"
                      :has-feedback="showAirPortHelp ? true : false"
                      :help="showAirPortHelp ? '请先选择城市': ''"
                    >
                    <airport-city :showInternational="false" :travelType="travelType" :teamForm="creatTripParma" @chosedBeginCity="chosedBeginCityTicket" @chosedEndCity="chosedEndCityTicket"></airport-city>
                  </a-form-item>

                  <a-form-item
                      label=""
                      :validate-status="showDateHelp ? 'error' : 'success'"
                      :has-feedback="showDateHelp ? true : false"
                      :help="showDateHelp ? '请先选择时间': ''"
                    >
                    <date-picker :travelType="travelType" :planBeginDate="planBeginDate" :planEndDate="planEndDate" :beginTime="creatTripParma.beginDate" :endTime="creatTripParma.endDate" @change="travelTypeChange" @setBeginTime="setBeginTimeTicket" @setEndTime="setEndTimeTicket" />

                  </a-form-item>
                </a-form>
              </div>
              <div class="ticket-more">
                <div class="flex baseline">
                  <a-select class="more-cw" placeholder="更多" v-model:value="internationalCwdj" allow-clear >
                    <template #suffixIcon>
                      <DownOutlined :style="{ fontSize: '13px', fontWeight: 'bold' }" />
                    </template>
                    <a-select-option value="" class="option">不限</a-select-option>
                    <a-select-option value="1" class="option">普通经济舱</a-select-option>
                    <a-select-option value="4" class="option">高端经济舱</a-select-option>
                    <a-select-option value="2" class="option">公务舱</a-select-option>
                    <a-select-option value="3" class="option">头等舱</a-select-option>
                  </a-select>
                  <!-- <div>更多</div>
                      <img src="../../assets/image/banner/icon-bot.png" alt=""> -->
                </div>
                <!-- <div class="more-cw">

                    </div> -->
              </div>
              <div class="ticket-btn-box">
                <div v-if="travelReason == 1" class="ticket-btn-sea ticket-btn-sea-left" @click="ticketSearch(1)">
                  <img src="../../assets/image/banner/seach.png" alt="" />
                  <div>仅查询</div>
                </div>
                <div class="ticket-btn-sea" @click="ticketSearch(2)">
                  <img src="../../assets/image/banner/seach.png" alt="" />
                  <div>搜索</div>
                </div>
              </div>
            </div>
            <!-- 国际机票 -->
            <div class="contain-tickets-form" v-if="travelTypeAct == 1">
              <div class="ticket-form-top">
                <div>
                  <a-radio-group v-model:value="travelType">
                    <a-radio :value="1">单程</a-radio>
                    <a-radio :value="2">返程</a-radio>
                    <a-radio :value="3">多程</a-radio>
                  </a-radio-group>
                </div>
                <div class="travel-reason">
                  <div class="bgc-rea" :class="travelReason == 1 ? '' : 'bgc-rea-o'"></div>
                  <div
                    class="travel-reason-o"
                    @click="travelReason = 1"
                    :class="travelReason == 1 ? 'travel-reason-oa' : ''"
                  >
                    因公
                  </div>
                  <div
                    class="travel-reason-t"
                    @click="travelReason = 2"
                    :class="travelReason == 2 ? 'travel-reason-oa' : ''"
                  >
                    因私
                  </div>
                </div>
              </div>
              <div class="ticket-form-bottom">
                <a-form
                  :model="creatTripParma"
                  autocomplete="off"
                  layout="inline"
                  class="inline-form"
                >
                
                  <div class="flex go-group" :class="{'space-between': travelType !== 3}" >
                    <a-form-item
                      label=""
                      name="applyNo"
                      :validate-status="showApplyNoHelp ? 'error' : 'success'"
                      :has-feedback="showApplyNoHelp ? true : false"
                      :help="showApplyNoHelp ? '请先选择申请单': ''"
                    >
                     <apply-no-com key="gjjp" :travelType="travelType" @change="changeApplyNo" v-if="travelReason === 1"  />
                    </a-form-item>
                    
                    <traveler-select :class="{ 'left-gutter': travelType === 3 }" :travelType="travelType" @change="changeInTicket" :list="creatTripParma?.travelerList" v-if="travelReason === 1" />

                  </div>
                  <div class="flex go-group" :class="{ 'space-between': travelType != 3 }" v-for="(item, index) in multipleTrips" :key="item.key">
                    <international-airport-city :showInternational="false" :onlyInternational="true" :travelType="travelType" v-if="travelType != 3" :teamForm="creatTripParma" @chosedBeginCity="chosedBeginCityTicket" @chosedEndCity="chosedEndCityTicket"></international-airport-city>
                    <international-airport-city :showInternational="false" :onlyInternational="true" :travelType="travelType"  @chosedBeginCity="chosedBeginCityInternational($event, item)" @chosedEndCity="chosedEndCityInternational($event, item)" :teamForm="item" v-if="travelType === 3" :left-num="index + 1 + ''" />
                      <date-picker
                        :beginTime="item.beginDate"
                        @setBeginTime="setBeginTimeInternational($event, item)"
                        :travelType="travelType"
                        @change="travelTypeChange"
                        :class="{ 'left-gutter': travelType === 3 }"
                      />
                    <div class="delete flex" v-if="travelType === 3 && item.canDelete" @click="deleteTrip(item.key)">
                      <img src="../../assets/image/banner/delete.png" class="pointer" />
                    </div>
                  </div>

                </a-form>
                <div class="flex go-group" v-if="travelType === 3 && multipleTrips.length < 6">
                  <div class="add flex pointer" @click="addTrip">
                    <img src="../../assets/image/banner/add.png" class="pointer" />
                    <span>添加新行程</span>
                  </div>
                </div>
              </div>
              <div class="ticket-more">
                <div class="flex baseline">
                  <div class="link" @click="addInquirySheet">新增询价单</div>
                  <div class="link" @click="searchInquirySheet">查询询价单</div>
                  <a-select class="more-cw" placeholder="更多" allow-clear v-model:value="internationalCwdj">
                    <template #suffixIcon>
                      <DownOutlined :style="{ fontSize: '13px', fontWeight: 'bold' }" />
                    </template>
                    <a-select-option value="" class="option">不限</a-select-option>
                    <a-select-option value="1" class="option">普通经济舱</a-select-option>
                    <a-select-option value="4" class="option">高端经济舱</a-select-option>
                    <a-select-option value="2" class="option">公务舱</a-select-option>
                    <a-select-option value="3" class="option">头等舱</a-select-option>
                  </a-select>
                  <!-- <div>更多</div>
                      <img src="../../assets/image/banner/icon-bot.png" alt=""> -->
                </div>
              </div>
              <div class="ticket-btn-box">
                <div class="ticket-btn-sea" @click="internationalTicketSearch">
                  <img src="../../assets/image/banner/seach.png" alt="" />
                  <div>搜索</div>
                </div>
              </div>
            </div>
            <!-- 地面服务 -->
            <div class="contain-tickets-form" v-if="travelTypeAct == 2">
              <div class="ticket-form-top">
                <div></div>
                <div class="travel-reason">
                  <div class="bgc-rea" :class="travelReason == 1 ? '' : 'bgc-rea-o'"></div>
                  <div
                    class="travel-reason-o"
                    @click="travelReason = 1"
                    :class="travelReason == 1 ? 'travel-reason-oa' : ''"
                  >
                    因公
                  </div>
                  <div
                    class="travel-reason-t"
                    @click="travelReason = 2"
                    :class="travelReason == 2 ? 'travel-reason-oa' : ''"
                  >
                    因私
                  </div>
                </div>
              </div>
              <div class="ticket-form-bottom">
                <a-form
                  :model="creatTripParma"
                  autocomplete="off"
                  layout="inline"
                  class="inline-form"
                >
                  <a-form-item
                      label=""
                      name="applyNo"
                      :validate-status="showApplyNoHelp ? 'error' : 'success'"
                      :has-feedback="showApplyNoHelp ? true : false"
                      :help="showApplyNoHelp ? '请先选择申请单': ''"
                    >
                    <apply-no-com key="dmfw" @change="changeApplyNo" v-if="travelReason === 1" />
                  </a-form-item>

                  <a-form-item
                      label=""
                    >
                    <traveler-select @change="changeTicket" :list="creatTripParma?.travelerList" v-if="travelReason === 1" />
                  </a-form-item>

                  <a-form-item
                      label=""
                      :validate-status="showAirPortHelp ? 'error' : 'success'"
                      :has-feedback="showAirPortHelp ? true : false"
                      :help="showAirPortHelp ? '请先选择机场': ''"
                    >
                    <airport-select :form="creatTripParma" @chosedTerminal="chosedTerminal" :showInternational="false" />
                  </a-form-item>

                  <a-form-item
                      label=""
                      :validate-status="showDateHelp ? 'error' : 'success'"
                      :has-feedback="showDateHelp ? true : false"
                      :help="showDateHelp ? '请先选择出发日期': ''"
                    >
                    <service-date-picker @setTerminalDate="setTerminalDate" :planBeginDate="planBeginDate" :planEndDate="planEndDate" :terminalDate="creatTripParma?.terminalDate" />
                  </a-form-item>

                </a-form>


              </div>
              <div class="ticket-btn-box">
                <div class="ticket-btn-sea" @click="terminalSearch">
                  <img src="../../assets/image/banner/seach.png" alt="" />
                  <div>搜索</div>
                </div>
              </div>
            </div>

          </div>
          <!-- 酒店 -->
          <div class="banner-contain-hotel banner-contain-tickets" style="padding:0;" v-if="activeBaner == 2">
            <div class="ban-con-title">


              <img
              src="../../assets/image/banner/travel-type-one.png"
              class="tickets-bg"
              alt=""
              v-show="hotelTitleAct == 0"
            />
           
            <div class="contain-tickets-title" style="width: 320px;">
              <div
                v-for="(item, index) in hotelTitleList"
                :key="index"
                class="tickets-title-item"
                :class="hotelTitleAct == index ? 'tickets-title-item-act' : ''"
                @click="handelHotelTitleChange(index)"
              >
                {{ item }}
                <div class="border-t" v-if="hotelTitleAct == index"></div>
              </div>
            </div>

              <div class="ban-title-right" style="margin-top: 12px;">
                <div class="bgc-rea" :class="travelReason == 1 ? '' : 'bgc-rea-o'"></div>
                <div
                  class="travel-reason-o"
                  @click="travelReason = 1"
                  :class="travelReason == 1 ? 'travel-reason-oa' : ''"
                >
                  因公
                </div>
                <div
                  class="travel-reason-t"
                  @click="travelReason = 2"
                  :class="travelReason == 2 ? 'travel-reason-oa' : ''"
                >
                  因私
                </div>
              </div>

            </div>
            <div class="hotel-form-bottom" :class="travelReason == 2 ?'hotel-form-bottom-bottom':''">
              <a-form
                :model="creatTripParma"
                autocomplete="off"
                layout="inline"
                class="inline-form"
              >
              <a-form-item
                      label=""
                      :validate-status="showApplyNoHelp ? 'error' : 'success'"
                      :has-feedback="showApplyNoHelp ? true : false"
                      :help="showApplyNoHelp ? '请先选择申请单': ''"
                    >
                    <apply-no-com key="hotel" :travelType="travelType" @change="changeApplyNo" v-if="travelReason === 1" />
                  </a-form-item>

                  <a-form-item
                      label=""
                  >
                    <traveler-select :travelType="travelType" @change="changeTicket"  :list="creatTripParma?.travelerList" v-if="travelReason === 1" />
                  </a-form-item>
                
                  <a-form-item
                      label=""
                      :validate-status="showAirPortHelp ? 'error' : 'success'"
                      :has-feedback="showAirPortHelp ? true : false"
                      :help="showAirPortHelp ? '请先选择目的地': ''"
                  >
                    <destination-level :level="hotelLevel" :form="creatTripParma" :chosedCityName="hotelChosedCityName" @chosedHotelCity="chosedHotelCity" @changeLevel="changeLevel" />
                  </a-form-item>

                  <a-form-item
                      label=""
                      :validate-status="showDateHelp ? 'error' : 'success'"
                      :has-feedback="showDateHelp ? true : false"
                      :help="showDateHelp ? '请先选择入住、离店日期': ''"
                  >
                   <check-in-out :hotelInDate="hotelInDate" :hotelLeaveDate="hotelLeaveDate" @setHotelInDate="setHotelInDate" @setHotelLeaveDate="setHotelLeaveDate" />
                  </a-form-item>

                  <a-form-item
                      label=""
                  >
                    <keyword @keyWordChange="hotelKeyWordChange" />
                  </a-form-item>

              </a-form>
            </div>
            <div class="ticket-btn-box">
              <div class="ticket-btn-sea" @click="hotelSearch(1)" >
                <img src="../../assets/image/banner/seach.png" alt="" />
                <div>仅查询</div>
              </div>
              <div class="ticket-btn-sea" @click="hotelSearch(2)">
                <img src="../../assets/image/banner/seach.png"  alt="" />
                <div>搜索</div>
              </div>
            </div>
          </div>
          <!-- 火车 -->
          <div class="banner-contain-hotel banner-contain-train banner-contain-tickets" v-if="activeBaner == 3">
            <div class="ban-con-title">
              <div class="ban-title-left">
                <div class="ban-border"></div>
                <span class="ttile">火车票</span>
              </div>
              <div class="banH-title-rights">
                <div class="travel-reason-o">因公</div>
              </div>
            </div>
            
            <div class="ticket-form-bottom">
              <a-form
                autocomplete="off"
                layout="inline"
                class="inline-form"
              >

                <a-form-item
                  label=""
                  :validate-status="showApplyNoHelp ? 'error' : 'success'"
                  :has-feedback="showApplyNoHelp ? true : false"
                  :help="showApplyNoHelp ? '请先选择申请单': ''"
                >
                  <apply-no-com key="train" :travelType="travelType" @change="changeApplyNo" v-if="travelReason === 1" />
                </a-form-item>

                <a-form-item
                  label=""
                >
                  <traveler-select :travelType="travelType" @change="changeTicket"  :list="creatTripParma?.travelerList" v-if="travelReason === 1" />
                </a-form-item>

                <a-form-item
                  label=""
                  :validate-status="showAirPortHelp ? 'error' : 'success'"
                  :has-feedback="showAirPortHelp ? true : false"
                  :help="showAirPortHelp ? '请先选择城市': ''"
                >
                  <train-city :travelType="travelType" :teamForm="creatTripParma" @chosedBeginCity="chosedBeginCityTrain" @chosedEndCity="chosedEndCityTrain" @chosedBeginTrainStation="chosedBeginTrainStation" @chosedEndTrainStation="chosedEndTrainStation"></train-city>
                </a-form-item>

                <a-form-item
                  label=""
                  :validate-status="showDateHelp ? 'error' : 'success'"
                  :has-feedback="showDateHelp ? true : false"
                  :help="showDateHelp ? '请先选择出发日期': ''"
                >
                  <date-picker :planBeginDate="planBeginDate" :planEndDate="planEndDate" @setBeginTime="setBeginTimeTrain" :beginTime="creatTripParma.beginDate" :showReturn="false" />
                </a-form-item>
            
              </a-form>


            </div>
            <div class="ticket-btn-box">
              <div class="ticket-btn-sea" @click="trainSearch(1)">
                <img src="../../assets/image/banner/seach.png" alt="" />
                <div>仅查询</div>
              </div>
              <div class="ticket-btn-sea" @click="trainSearch(2)">
                <img src="../../assets/image/banner/seach.png" alt="" />
                <div>搜索</div>
              </div>
            </div>
          </div>

          <!-- 团队 -->
          <div class="banner-contain team-contain" v-if="activeBaner == 4">
            <div class="ban-con-title">
              <div class="ban-title-left">
                <div class="ban-border"></div>
                <span class="ttile">团队申请单</span>
              </div>
              <div class="ban-title-right pointer" @click="goToTeamList(activeApply?.id)">
                <span>单据中心</span>
                <img src="../../assets/image/banner/right.png" alt="" class="" />
              </div>
            </div>
            

            <div  class="banner-contain-bottom team-content ">
              
              <div class="ticket-form-top">
                <div>
                  <a-radio-group v-model:value="teamForm.teamDestinePlaneTicket.voyageType">
                    <a-radio :value="0">单程</a-radio>
                    <a-radio :value="1">返程</a-radio>
                  </a-radio-group>
                </div>
                <div class="travel-reason">
                  <div class="bgc-rea" :class="teamForm.evectionType == 0 ? '' : 'bgc-rea-o'"></div>
                  <div
                    class="travel-reason-o"
                    @click="teamForm.evectionType = 0"
                    :class="teamForm.evectionType == 0 ? 'travel-reason-oa' : ''"
                  >
                    因公
                  </div>
                  <div
                    class="travel-reason-t"
                    @click="teamForm.evectionType = 1"
                    :class="teamForm.evectionType == 1 ? 'travel-reason-oa' : ''"
                  >
                    因私
                  </div>
                </div>
              </div>

              <div class="ticket-form-bottom">
              <team-city :teamForm="teamForm" @chosedBeginCity="chosedBeginCity" @chosedEndCity="chosedEndCity"></team-city>
              <date-picker-team @setBeginTime="setBeginTime" @setEndTime="setEndTime" :travelType="teamForm.teamDestinePlaneTicket.voyageType ? 2 : 1" :showReturn="false" />
            </div>


            </div>
          

            <div
              class="add-apply pointer"
              @click="saveAndGoAddTeam"
              @mousemove="isApplyHover = true"
              @mouseleave="isApplyHover = false"
            >
              <img src="../../assets/image/banner/add-bot.png" alt="" />
              <span>创建新团队申请单</span>
            </div>
          </div>

        </div>
        <div class="info">
          <div class="info-tit">
            <div class="info-tit-l">
              <img src="../../assets/image/banner/info-tit.png" alt="" />
              <div>政策指导</div>
            </div>
            <div class="info-tit-r">
              <span>查看更多</span>
              <img src="../../assets/image/banner/right.png" alt="" />
            </div>
          </div>
          <div
            class="info-main"
            v-for="(item, index) in infoDataSource"
            :key="index"
            @click="gotoUrlOrDetail(item, '/travel/infoDetail')"
          >
            <div class="info-list-name">{{ item.infoTitle }}</div>
            <div class="info-list-time">{{ item.infoDate }}</div>
          </div>
          <div class="info-bor"></div>
        </div>
      </div>
      <img :src="applyBackground" class="absolute banner-background" :class="activeBaner === 0 ? 'show' : 'hide'" />
      <img :src="flightBackground" class="absolute banner-background" :class="activeBaner === 1 ? 'show' : 'hide'" />
      <img :src="hotelBackground" class="absolute banner-background" :class="activeBaner === 2 ? 'show' : 'hide'" />
      <img :src="trainBackground" class="absolute banner-background" :class="activeBaner === 3 ? 'show' : 'hide'" />
    </div>
    <!-- 头部展示数据,为了用户部分上线展示 -->
    <div class="row" v-else>
          <div class="banner2">
            <h-carousel :autoplay="true" arrows>
              <template #prevArrow>
                <div :class="isBannerHover ? 'custom-slick-arrow' : ''" style="left: 10px; z-index: 1" @mousemove="setIsBannerHover(true)">
                  <LeftOutlined />
                </div>
              </template>
              <template #nextArrow>
                <div :class="isBannerHover ? 'custom-slick-arrow' : ''" style="right: 10px" @mousemove="setIsBannerHover(true)">
                  <RightOutlined />
                </div>
              </template>
              <div class="advertisement pointer" v-for="(item , index) in dataSource" :key="index"  @mousemove="setIsBannerHover(true)" @mouseleave="setIsBannerHover(false)" @click="gotoUrlOrDetail(item, '/travel/adDetail')">
                <img :src="item.imgUrl" class="img">
              </div>
            </h-carousel>
          </div>
          <div class="info">
            <div class="card-header">
              <div class="card-title">
                商务资讯
              </div>
              <div class="card-more" @click="gotoUrl('/travel/index')">
                查看更多>
              </div>
            </div>
            <div class="card-con">
              <div class="info-con pointer" v-for="(item, index) in infoDataSource" :key="index" @click="gotoUrlOrDetail(item, '/travel/infoDetail')">
                <div class="info-left" >
                  <div class="info-title">
                    {{ item.infoTitle }}
                  </div>
                  <div class="info-time">
                    {{ item.infoAuthor }} {{ item.infoDate }}
                  </div>
                </div>
                <div class="info-right">
                  <img :src="item.imgUrl" class="info-img" />
                </div>
              </div>
            </div>
          </div>
        </div>
    <div class="row">
      <div class="swiper-con">
        <div class="card-header">
          <div class="card-title">热门推荐</div>
          <div class="card-more"></div>
        </div>
        <div class="card-con" @mousemove="setIsSwiperHover(true)" @mouseleave="setIsSwiperHover(false)">
          <swiper
            class="swiper-width"
            :modules="modules"
            :slides-per-view="3"
            :space-between="50"
            :navigation="isSwiperHover"
          >
            <swiper-slide class="swiper-hotel">
              <div
                class="hotel pointer"
                @click="openNewWindow('https://businesstravel.haier.net/localhotel/#/hoteldetail?id=78')"
              >
                <div class="hotel-img">
                  <img :src="hotel" class="img" />
                </div>
                <div class="hotel-title">
                  <div class="hotel-name">海尔山庄</div>
                  <div class="hotel-address">青岛崂山仰口风景区</div>
                </div>
              </div>
            </swiper-slide>
            <swiper-slide class="swiper-hotel">
              <div
                class="hotel pointer"
                @click="openNewWindow('https://businesstravel.haier.net/localhotel/#/hoteldetail?id=146')"
              >
                <div class="hotel-img">
                  <img :src="hotel1" class="img" />
                </div>
                <div class="hotel-title">
                  <div class="hotel-name">沧海之粟</div>
                  <div class="hotel-address">青岛市崂山区东海东路52号</div>
                </div>
              </div>
            </swiper-slide>
            <swiper-slide class="swiper-hotel">
              <div
                class="hotel pointer"
                @click="openNewWindow('https://businesstravel.haier.net/localhotel/#/hoteldetail?id=145')"
              >
                <div class="hotel-img">
                  <img :src="hotel2" class="img" />
                </div>
                <div class="hotel-title">
                  <div class="hotel-name">南海德音（南海路）</div>
                  <div class="hotel-address">青岛市市南区南海路6号</div>
                </div>
              </div>
            </swiper-slide>
            <swiper-slide class="swiper-hotel">
              <div
                class="hotel pointer"
                @click="openNewWindow('https://businesstravel.haier.net/localhotel/#/hoteldetail?id=83')"
              >
                <div class="hotel-img">
                  <img :src="hotel3" class="img" />
                </div>
                <div class="hotel-title">
                  <div class="hotel-name">海尔洲际酒店</div>
                  <div class="hotel-address">青岛市市南区澳门路98号</div>
                </div>
              </div>
            </swiper-slide>
          </swiper>
        </div>
      </div>
      <div class="life">
        <div class="card-header">
          <div class="card-title">服务公告</div>
          <div class="card-more"></div>
        </div>
        <div class="life-con">
          <h-carousel :autoplay="true" arrows>
            <template #prevArrow>
              <div
                :class="isLifeHover ? 'custom-slick-arrow' : ''"
                style="left: 10px; z-index: 1"
                @mousemove="setIsLifeHover(true)"
              >
                <LeftOutlined />
              </div>
            </template>
            <template #nextArrow>
              <div
                :class="isLifeHover ? 'custom-slick-arrow' : ''"
                style="right: 10px"
                @mousemove="setIsLifeHover(true)"
              >
                <RightOutlined />
              </div>
            </template>
            <div
              class="life-img pointer"
              v-for="(item, index) in lifeDataSource"
              :key="index"
              @mousemove="setIsLifeHover(true)"
              @mouseleave="setIsLifeHover(false)"
              @click="gotoUrlOrDetail(item, '/travel/lifeDetail')"
            >
              <img :src="item.imgUrl" class="img" />
            </div>
          </h-carousel>
        </div>
      </div>
    </div>
    <div class="row">
      <activity />
      <you-xuan />
    </div>

    <h-modal v-model:open="reimburseDialog" style="width:500px;" title="去报销" @cancel="closeReimburseDialog" @ok="goToEES">
      <h-row style="margin-bottom: 20px; margin-top: 10px;">
        <h-col :span="24">
          <h-alert message="每人每个申请单最多可报销两次" type="warning" show-icon />
        </h-col>

      </h-row>
      <h-row>
        <h-col :span="6" class="flex" style="align-items: center; text-align: right; padding-right: 10px;">报销人:</h-col>
        <h-col :span="18">
          <h-select v-model:value="reimburseUserCode" placeholder="请选择报销人" style="width: 100%">
            <h-select-option v-for="item in ReimburseTravelerList" :key="item.travelUserNo" :value="item.travelUserNo">{{ item.travelUserName }} / {{ item.travelUserNo }}</h-select-option>
          </h-select>
        </h-col>
      </h-row>
    </h-modal>
  </div>
</template>

<style scoped lang="less">
.baseline {
  align-items: baseline;
}

.space-between {
  justify-content: space-between;
}

.flex {
  display: flex;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.show {
  opacity: 1;
  transition: all 1s;
}

.hide {
  opacity: 0;
  transition: all 1s;
}

.pointer {
  cursor: pointer;
}

.sub-title {
  color: #3983e5;
  font-size: 14px;
  font-weight: 400;
}

.inline-form :deep(.ant-form-item) {
  margin-right: 2px !important;
}

.container {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  background-color: #f5f5f5;
  padding-bottom: 85px;

  .row {
    width: 1280px;
    margin-top: 40px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-top: 24px;

    .banner {
      width: 1280px;
      height: 410px;
      background: linear-gradient(272deg, rgba(71, 78, 146, 0) 0%, #222659 100%);
      /* opacity: 0.3; */
      border-radius: 8px;
      padding: 44px 24px;
      box-sizing: border-box;
      display: flex;
      z-index: 2;

      .more-cw {
        width: 130px;
      }

      .more-cw-short {
        width: 70px;
      }

      .banner-list {
        width: 872px;
        .bannet-list-top {
          display: flex;
          margin-bottom: 14px;
          .banner-list-item {
            display: flex;
            align-items: center;
            margin-right: 34px;
            font-weight: 500;
            font-size: 20px;
            color: #ffffff;
            width: 108px;
            height: 42px;
            border-radius: 21px;
            cursor: pointer;
            &:first-child {
              width: 150px;
            }
            .banner-img-item {
              width: 38px;
              height: 38px;
              margin-right: 10px;
            }
            .banner-img-item0 {
              width: 56px;
              height: 50px;
              margin-right: 0px;
              position: relative;
              left: -2px;
            }
          }
        }
        .banner-contain {
          width: 872px;
          height: 257px;
          padding-top: 16px;
          box-sizing: border-box;
          background-image: url('@/assets/image/banner/baner-contain-bac.png');
          background-size: 100% 100%;

          .banner-contain-empty {
            height: 174px;
            padding: 14px 12px;
            .banner-contain-empty-text {
              font-family: PingFangSC, PingFang SC;
              font-size: 14px;
              color: #595959;
              font-weight: 500;
              margin-bottom: 13px;
            }
            .banner-contain-step-image {
              display: flex;
              img {
                // width: 602px;
                width: 720px;
                height: 82px;
                margin: 0 auto;
              }
            }
          }
          .ban-con-title {
            display: flex;
            justify-content: space-between;
            padding-right: 24px;
            position: relative;
            .ban-title-left {
              font-weight: 600;
              font-size: 18px;
              color: #3983e5;
              padding-left: 24px;
              .ban-border {
                position: absolute;
                top: 0;
                left: 2px;
                width: 4px;
                height: 21px;
                background: linear-gradient(180deg, #3983e5 0%, #6bb9f4 100%);
              }
            }
            .ban-title-right {
              /* font-weight: 500; */
              font-size: 14px;
              color: #3983e5;
              display: flex;
              align-items: center;
              img {
                width: 18px;
                height: 18px;
              }
            }
          }
          .banner-contain-bottom {
            width: 824px;
            margin-left: 24px;
            background-color: #fff;
            border-radius: 8px;
            margin-top: 14px;
            .banner-contain-data {
              display: flex;
              height: 40px;
              align-items: center;
              justify-content: space-between;
              padding: 0 6px;
              color: #262626;
              font-weight: 400;
              border-bottom: 1px solid #eee;
              div {
                display: flex;
                align-items: center;
              }
              .data-one {
                width: 210px;
                height: 28px;
                background: #fafafa;
                border-radius: 2px;
                box-sizing: border-box;
                padding-left: 4px;
                :deep(.ant-select-selector) {
                  width: 210px;
                  height: 28px !important;
                  line-height: 28px;
                  background: #fafafa;
                  border: 0;
                  padding: 0 5px;
                }
                :deep(.ant-select-item-option-content) {
                  font-size: 12px;
                }
                :deep(.ant-select-selection-item) {
                  font-size: 12px;
                  line-height: 28px;
                  font-weight: 500;
                  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0) !important;
                }
              }
              .data-time {
                font-size: 13px;
                margin-left: 15px;
                img {
                  width: 14px;
                  height: 14px;
                  margin-right: 2px;
                }
                .peice-mar {
                  margin-right: 8px;
                  font-size: 12px;
                  display: flex;
                  align-items: center;
                  line-height: 16px;
                }
              }
              .data-btn {
                display: flex;
                margin-left: 20px;
                .data-sta {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  width: 78px;
                  height: 22px;
                  font-size: 12px;
                  color: #10a710;
                  background: #f6ffed;
                  border-radius: 2px;
                  border: 1px solid #b7eb8f;
                  img {
                    width: 12px;
                    height: 12px;
                  }
                }
                .data-tra {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  width: 52px;
                  height: 22px;
                  background: #f0f9ff;
                  border-radius: 2px;
                  border: 1px solid #a1d1ff;
                  font-size: 12px;
                  color: #0073e5;
                  margin-left: 4px;
                }
              }
            }
            .banner-detail {
              width: 824px;
              height: 92px;
              background: #ffffff;
              padding-left: 42px;
              padding-top: 12px;
              box-sizing: border-box;
              font-weight: 400;
              display: flex;
              border-bottom: 1px solid #eee;
              justify-content: space-between;
              padding-right: 16px;
              .detail-info-i {
                display: flex;
                font-size: 14px;
                color: #8c8c8c;
                height: 20px;
                align-items: center;
                margin-bottom: 5px;

                .info-i-l {
                  display: flex;
                  align-items: center;
                  width: 84px;
                }
                img {
                  width: 16px;
                  height: 16px;
                }
                .names {
                  color: #262626;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  overflow: hidden;
                }
                .info-main {
                  width: 80px;
                  font-family: 'Microsoft YaHei';
                  /* margin-left: 8px; */
                }
              }
            }
            .banner-step {
              display: flex;

              width: 581px;
              height: 72px;
              background: #fafafa;
              border-radius: 4px;
              display: flex;
              box-sizing: border-box;
              justify-content: start;
              padding-left: 40px;
              color: rgba(0, 0, 0, 0.85);
              font-family: 'Microsoft YaHei';
              .step-item {
                display: flex;
                min-width: 200px;
                // padding-top: 15px;
                /* &:last-child{
                  width: 100px;
                } */
              }
              .step-name{
                padding-top: 0;
                width: 45px;
                position: relative;
                > span {
                  // font-size: 14px;
                }
                .hotal-img {
                  width: 24px;
                  height: 24px;
                  margin-left: 4px;
                }
                .step-c {
                  position: absolute;
                  // bottom: 32px;
                  top:12px;
                  left: 0;
                  width: 60px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  .city-name {
                    flex: 1;
                    overflow: hidden;
                    display: inline-block;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                  }
                }

                .step-t {
                  position: absolute;
                  font-size: 14px;
                  color: #8c8c8c;
                  bottom: 14px;
                  left: 0;
                  width: 140px;
                }
              }
              .step-item-start {
                min-width: 60px;
                padding-top: 0px;
              }
              .idol {
                border-bottom: 1px dashed #d9d9d9;
                display: flex;
                align-items: center;
                justify-content: center;
                flex: 1;
                height: 11px;
                max-width: 100%;
                min-width: 100px;
                margin: 15px 23px 0 23px;
                .idol-img{
                  position: relative;
                  top: 3px;
                  /* position: absolute;
                  top: -2px;
                  left: 50%;
                  transform: translateX(-50%); */
                }
                img {
                  width: 24px;
                  height: 24px;
                  margin-left: 4px;
                }
              }
            }
          }
          .btn-contain {
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 16px;
            .btn {
              cursor: pointer;
              width: 76px;
              height: 24px;
              background: linear-gradient(180deg, #6bb9f4 0%, #3983e5 100%);
              display: flex;
              justify-content: center;
              align-items: center;
              font-size: 14px;
              color: #ffffff;
              font-weight: 400;
              border-radius: 3px;
              img {
                width: 14px;
                height: 14px;
                margin-right: 4px;
              }
            }
            .btn-yu {
              width: 70px;
              margin-left: 6px;
              position: relative;
              .card-list {
                position: absolute;
                top: 28px;
                right: -10px;
                z-index: 999;
                width: 340px;
                /* height: 202px; */
                background: #ffffff;
                box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08),
                  0px 3px 6px -4px rgba(0, 0, 0, 0.12);
                border-radius: 8px;
                padding: 20px;
                box-sizing: border-box;

                .triangle-con {
                  position: absolute;
                  width: 70px;
                  height: 15px;
                  top: -15px;
                  right: 10px;
                }

                .triangle {
                  float: right;
                  margin-right: 10px;
                  margin-top: 5px;
                }
                .card-list-item {
                  display: flex;
                  justify-content: space-between;
                  flex-direction: row;
                  border-bottom: 1px solid #eee;
                  padding-bottom: 17px;
                  margin-top: 12px;
                  // margin-left: 12px;
                  &:first-child {
                    margin-top: 0px;
                  }
                  &:last-child {
                    border-bottom: 0px;
                    padding-bottom: 0px;
                  }
                  /* align-items: center; */
                  .item-l {
                    width: 34px;
                    height: 34px;
                    margin-right: 0;
                  }
                  .banner-step {
                    width: 210px;
                    height: 16px;
                    display: flex;
                    box-sizing: border-box;
                    flex-direction: row;
                    justify-content: start;
                    padding: 0;
                    padding-left: 12px;
                    background: #fff;
                    font-family: 'Microsoft YaHei';
                    .step-name_ {
                      width: 75px;
                      position: relative;
                      span {
                overflow: hidden;
                white-space: nowrap;
                display: inline-block;
                width: 100%;
                text-overflow: ellipsis;
              }
                      .step-t {
                        position: absolute;
                        font-size: 12px;
                        color: #8c8c8c;
                        bottom: -16px;
                        left: 0;
                        width: 55px;
                        font-weight: 400;
                      }
                    }
                    .idol_ {
                      width: 40px;
                      border-bottom: 1px dashed #d9d9d9;
                      position: relative;
                      margin: 0 7px;
                      top: -6px;
                    }
                  }
                  .btn-step_ {
                    width: 44px;
                    height: 24px;
                    background: rgba(107, 185, 244, 0.1);
                    border-radius: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: rgba(57, 131, 229, 0.8);
                    font-size: 14px;
                  }
                  .btn-reserve {
                    background: linear-gradient(180deg, #6bb9f4 0%, #3983e5 100%);
                    border-radius: 12px;
                    color: #fff;
                  }
                }
              }
              .card-more-ban {
                width: 82px;
                // height: 100px;
                background: #ffffff;
                box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08),
                  0px 3px 6px -4px rgba(0, 0, 0, 0.12);
                border-radius: 4px;
                position: absolute;
                top: 30px;
                right: -10px;
                padding: 13px 17px 0;
                box-sizing: border-box;

                .triangle-con {
                  position: absolute;
                  width: 70px;
                  height: 15px;
                  top: -15px;
                  right: 9px;
                }

                .triangle {
                  float: right;
                  margin-right: 10px;
                  margin-top: 5px;
                }

                // .triangle{
                //   position: absolute;
                //   width:20px;
                //   height: 20px;
                //   top: -15px;
                //   right: 20px;
                // }
                .more-ban-items:hover {
                  color: rgba(255, 77, 79, 1);
                }
                .more-ban-items {
                  display: flex;
                  // justify-content: space-between;
                  align-items: center;
                  margin-bottom: 11px;
                  font-size: 14px;
                  color: rgba(0, 0, 0, 0.85);
                  img {
                    width: 14px;
                    height: 14px;
                  }
                }
              }
            }
            .btn-yu-max {
              width: 100px;
            }

            .btn-et:hover {
              border: 1px solid #2793f2;
            }

            .btn-et {
              border: 1px solid #3983e5;
              background: #fff;
              margin-left: 6px;
              color: #3983e5;
            }
            .btnMouseInter {
              color: #40a9ff;
              border: 1px solid #40a9ff;
            }
          }

          .add-apply:hover {
            background: linear-gradient(180deg, #3983e5 0%, #6bb9f4 100%);
          }

          .add-apply {
            width: 202px;
            height: 48px;
            margin-top: 8px;
            margin: 8px auto 0;
            background: linear-gradient(180deg, #6bb9f4 0%, #3983e5 100%);
            box-shadow: 0px 2px 8px 0px rgba(60, 134, 230, 0.26);
            border-radius: 24px;
            font-weight: 500;
            font-size: 18px;
            color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            box-sizing: border-box;
            img {
              width: 28px;
              height: 28px;
              margin-right: 8px;
            }
          }
        }
        .banner-contain-tickets {
          position: relative;

          .tickets-bg {
            position: absolute;
            top: -4px;
            left: 0;
            width: 872px;
            height: 75px;
          }
          .contain-tickets-title {
            width: 700px;
            height: 57px;
            display: flex;
            justify-content: space-between;
            background-color: #fff;
            align-items: center;
            box-sizing: border-box;
            border-radius: 12px 12px 0 0;
            background: rgba(255, 255, 255, 0.8);
            .tickets-title-item {
              flex: 1;
              text-align: center;
              font-size: 18px;
              position: relative;
              height: 100%;
              padding-top: 10px;
              box-sizing: border-box;
              /* line-height: 47px; */
              cursor: pointer;
              color: #595959;
              .border-t {
                width: 28px;
                height: 4px;
                background: linear-gradient(90deg, #6bb9f4 0%, #3983e5 100%);
                border-radius: 2px;
                position: absolute;
                bottom: 10px;
                left: 50%;
                transform: translateX(-50%);
              }
            }
            .tickets-title-item-act {
              color: #3983e5;
            }
          }
          .contain-tickets-form {
            position: relative;
            width: 872px;
            min-height: 199px;
            background: #ffffff;
            border: 2px solid #ffffff;
            border-radius: 0 8px 8px 8px;
            padding: 0px 24px 0;
            .ticket-form-top {
              display: flex;
              justify-content: space-between;
              z-index: 1;
              margin-bottom: 13px;
              .travel-reason {
                width: 90px;
                height: 24px;
                background: #f5f5f5;
                border-radius: 12px;
                padding: 0 6px;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                font-size: 14px;
                color: #8c8c8c;
                position: relative;
                z-index: 10;
                text-align: center;
                position: relative;
                cursor: pointer;
                .bgc-rea {
                  position: absolute;
                  width: 46px;
                  height: 20px;
                  background: #ffffff;
                  box-shadow: 0px 2px 6px 0px rgba(68, 87, 121, 0.2);
                  border-radius: 11px;
                  top: 2px;
                  left: 2px;
                  cursor: pointer;
                }
                .bgc-rea-o {
                  left: 42px;
                }
                .travel-reason-o {
                  flex: 1;
                  position: relative;
                  z-index: 99;
                }
                .travel-reason-t {
                  flex: 1;
                  position: relative;
                  z-index: 99;
                }
                .travel-reason-oa {
                  color: #3983e5;
                }
              }
            }
            .ticket-form-bottom {
              display: flex;
              flex-wrap: wrap;
              justify-content: space-between;

              .go-group {
                width: 100%;
                flex-direction: row;

                .left-gutter {
                  margin-left: 8px;
                }

                .delete {
                  align-items: center;
                  margin-left: 12px;
                  margin-bottom: 8px;

                  img {
                    width: 24px;
                    height: 24px;
                  }
                }

                .add {
                  align-items: center;
                  padding-left: 16px;
                  margin-left: 18px;
                  width: 770px;
                  height: 54px;
                  border-radius: 8px;
                  border: 1px dashed #d9d9d9;

                  img {
                    width: 16px;
                    height: 16px;
                  }

                  span {
                    font-size: 14px;
                    color: #3983e5;
                    margin-left: 8px;
                  }
                }
              }
            }
            .ticket-more {
              display: flex;
              justify-content: flex-end;
              font-weight: 400;
              font-size: 14px;
              margin-top: 3px;
              color: rgba(0, 0, 0, 0.65);
              align-items: center;
              cursor: pointer;
              .link {
                position: relative;
                z-index: 999;
                line-height: 28px;
                font-weight: 400;
                font-size: 14px;
                color: #3983e5;
                text-align: left;
                font-style: normal;
                text-decoration-line: underline;
                margin-left: 24px;
              }
              img {
                width: 16px;
                height: 16px;
                margin-bottom: 4px;
              }
            }
            .ticket-btn-box {
              position: absolute;
              display: flex;
              left: 284px;
              bottom: -24px;
              width: 304px;
              justify-content: center;
              font-weight: 500;
              .ticket-btn-sea-left {
                margin-right: 40px;
              }
              .ticket-btn-sea {
                cursor: pointer;
                width: 132px;
                height: 48px;
                background: linear-gradient(180deg, #6bb9f4 0%, #3983e5 100%);
                box-shadow: 0px 2px 8px 0px rgba(60, 134, 230, 0.26);
                border-radius: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #fff;
                img {
                  width: 24px;
                  height: 24px;
                  margin-right: 8px;
                }
              }
            }
          }
        }
        .banner-contain-hotel {
          width: 872px;
          height: 257px;
          padding-top: 16px;
          box-sizing: border-box;
          background-image: url('@/assets/image/banner/baner-contain-bac.png');
          background-size: 100% 100%;
          cursor: pointer;
          .ban-con-title {
            display: flex;
            justify-content: space-between;
            padding-right: 24px;
            position: relative;
            .ban-title-left {
              font-weight: 600;
              font-size: 18px;
              color: #3983e5;
              padding-left: 24px;
              .ban-border {
                position: absolute;
                top: 0;
                left: 2px;
                width: 4px;
                height: 21px;
                background: linear-gradient(180deg, #3983e5 0%, #6bb9f4 100%);
              }
            }
            .ban-title-right {
              width: 90px;
              height: 24px;
              background: #f5f5f5;
              border-radius: 12px;
              padding: 0 6px;
              box-sizing: border-box;
              display: flex;
              align-items: center;
              font-size: 14px;
              color: #8c8c8c;
              position: relative;
              z-index: 10;
              text-align: center;
              position: relative;
              cursor: pointer;
              .bgc-rea {
                position: absolute;
                width: 46px;
                height: 20px;
                background: #ffffff;
                box-shadow: 0px 2px 6px 0px rgba(68, 87, 121, 0.2);
                border-radius: 11px;
                top: 2px;
                left: 2px;
                cursor: pointer;
              }
              .bgc-rea-o {
                left: 42px;
              }
              .travel-reason-o {
                flex: 1;
                position: relative;
                z-index: 99;
              }
              .travel-reason-t {
                flex: 1;
                position: relative;
                z-index: 99;
              }
              .travel-reason-oa {
                color: #3983e5;
              }
            }
          }
          .hotel-form-bottom {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            // min-height: 186px;
            padding: 0 24px 0;

            background: #ffffff;
            border: 2px solid #ffffff;
            border-radius: 0 8px 8px 8px;
            :deep(input) {
              font-family: 'Microsoft YaHei';
            }
            
            .ant-calendar-input {
              font-size: 14px;
            }
            .ant-picker-focused {
              box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
            }
            .ticket-item {
              width: 408px;
              height: 54px;
              background: #ffffff;
              border-radius: 8px;
              border: 1px solid #d9d9d9;
              margin-bottom: 8px;
              padding: 8px 0 0 12px;
              box-sizing: border-box;
              position: relative;
              .add-return {
                position: absolute;
                bottom: 8px;
                right: 12px;
                font-weight: 500;
                font-size: 16px;
                color: rgba(0, 0, 0, 0.35);
              }
              .item-labels {
                font-size: 12px;
                color: rgba(0, 0, 0, 0.35);
                line-height: 17px;
              }
              .item-num {
                font-weight: 500;
                font-size: 16px;
                color: rgba(0, 0, 0, 0.85);
                line-height: 22px;
              }
            }
            .ticket-item-a {
              display: flex;
              position: relative;
              width: 408px;
              justify-content: space-between;
              position: relative;
              .ticket-item-ai {
                width: 200px;
                height: 54px;
                background: #ffffff;
                border-radius: 8px;
                border: 1px solid #d9d9d9;
                padding: 8px 0 0 12px;
                .item-labels {
                  font-size: 12px;
                  color: rgba(0, 0, 0, 0.35);
                  line-height: 17px;
                }
              }
              img {
                width: 28px;
                height: 28px;
                position: absolute;
                top: 14px;
                left: 50%;
                transform: translateX(-50%);
              }
            }
          }
          .hotel-form-bottom-bottom {
            padding-bottom: 70px;
          }
          .ticket-btn-box {
            display: flex;
            width: 304px;
            justify-content: space-between;
            font-weight: 500;
            margin: -5px auto;
            .ticket-btn-sea {
              width: 132px;
              height: 48px;
              background: linear-gradient(180deg, #6bb9f4 0%, #3983e5 100%);
              box-shadow: 0px 2px 8px 0px rgba(60, 134, 230, 0.26);
              border-radius: 24px;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #fff;
              img {
                width: 24px;
                height: 24px;
                margin-right: 8px;
              }
            }
          }
        }
        .banner-contain-train {
          .travel-reason-o {
            width: 46px;
            height: 20px;
            background: #ffffff;
            box-shadow: 0px 2px 6px 0px rgba(68, 87, 121, 0.2);
            border-radius: 11px;
            line-height: 20px;
            font-weight: 600;
            font-size: 14px;
            color: #3983e5;
            text-align: center;
          }
          .ticket-form-bottom {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            padding: 12px 24px;

            .ant-calendar-input {
              font-size: 14px;
            }
            .ant-picker-focused {
              box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
            }
            .ticket-item {
              width: 408px;
              height: 54px;
              background: #ffffff;
              border-radius: 8px;
              border: 1px solid #d9d9d9;
              margin-bottom: 8px;
              padding: 8px 0 0 12px;
              box-sizing: border-box;
              position: relative;
              .add-return {
                position: absolute;
                bottom: 8px;
                right: 12px;
                font-weight: 500;
                font-size: 16px;
                color: rgba(0, 0, 0, 0.35);
              }
              .item-labels {
                font-size: 12px;
                color: rgba(0, 0, 0, 0.35);
                line-height: 17px;
              }
              .item-num {
                font-weight: 500;
                font-size: 16px;
                color: rgba(0, 0, 0, 0.85);
                line-height: 22px;
              }
            }
            .ticket-item-a {
              display: flex;
              position: relative;
              width: 408px;
              justify-content: space-between;
              position: relative;
              .ticket-item-ai {
                width: 200px;
                height: 54px;
                background: #ffffff;
                border-radius: 8px;
                border: 1px solid #d9d9d9;
                padding: 8px 0 0 12px;
                .item-labels {
                  font-size: 12px;
                  color: rgba(0, 0, 0, 0.35);
                  line-height: 17px;
                }
              }
              img {
                width: 28px;
                height: 28px;
                position: absolute;
                top: 14px;
                left: 50%;
                transform: translateX(-50%);
              }
            }
          }
          .ticket-btn-box {
            margin-top: 50px;
          }
        }
      }

      .bannerMouse {
        background: rgba(255, 255, 255, 0.2);
      }

      .bannerActive {
        background: linear-gradient(180deg, #6bb9f4 0%, #3983e5 100%);
        box-shadow: 0px 2px 4px 0px rgba(57, 131, 229, 0.26);
      }

      .info {
        background-image: url('@/assets/image/banner/ban-right.png');
        background-size: 100% 100%;
        width: 336px;
        height: 314px;
        margin-left: 24px;
        padding: 24px;
        box-sizing: border-box;
        .info-tit {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 28px;
          .info-tit-l {
            display: flex;
            font-weight: 500;
            font-size: 20px;
            color: #000000;
            align-items: center;
            img {
              width: 28px;
              height: 28px;
              margin-right: 8px;
            }
          }
          .info-tit-r {
            display: flex;
            font-weight: 500;
            font-size: 14px;
            color: #3983e5;
            align-items: center;
            cursor: pointer;
            img {
              width: 20px;
              height: 20px;
            }
          }
        }
        .info-main {
          margin-top: 24px;
          margin-bottom: 33px;
          cursor: pointer;
          font-family: 'Microsoft YaHei';
          .info-list-name {
            font-weight: 500;
            font-size: 14px;
            color: #000000;
            line-height: 20px;
            margin-bottom: 4px;
          }
          .info-list-time {
            font-weight: 400;
            font-size: 14px;
            color: #bfbfbf;
            line-height: 20px;
          }
        }
      }
    }
    .banner2 {
      width: 888px;
      height: 427px;
      border-radius: 8px;

      .advertisement {
        position: relative;
        width: 100%;
        height: 100%;
        border-radius: 8px;

        .img {
          width: 889px;
          height: 427px;
          border-radius: 8px;
          padding-left: 1px;
          padding-right: 1px;
        }

        .desc {
          position: absolute;
          width: 600px;
          height: 120px;
          left: 20px;
          bottom: 20px;
          background: rgba(0, 0, 0, 0.35);
          padding: 21px 17px 26px 26px;
          color: #FFF;

          .banner-title {
            
            font-size: 20px;
            font-weight: 500;
            line-height: 20px;
          }

          .banner-desc {
            font-size: 14px;
            font-weight: 400;
            line-height: 21px;
            margin-top: 17px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
        }
      }
    }


    .banner-background {
      top: 0;
      left: 0;
      width: 100%;
      height: 410px;
      z-index: 0;
    }

    .card-header {
      display: flex;
      width: 100%;
      align-items: center;
      justify-content: space-between;

      .card-title {
        color: #000;
        font-size: 24px;
        font-weight: 600;
        line-height: 20px;
        display: flex;
      }

      .card-more {
        display: flex;
        color: #3983e5;
        font-size: 14px;
        font-weight: 400;
        line-height: 14px;
        cursor: pointer;
      }
    }

    .info {
      width: 349px;
      height: 427px;

      .card-con {
        margin-top: 16px;
        display: flex;
        width: 100%;
        height: 388px;
        border-radius: 8px;
        background: #fff;
        padding: 0px 30px;
        flex-direction: column;

        .info-con:last-child {
          border-bottom: 0px solid rgba(0, 0, 0, 0.06);
        }

        .info-con {
          display: flex;
          flex-direction: row;
          height: 84px;
          margin-top: 28px;
          width: 100%;
          align-items: center;
          justify-content: space-between;
          border-bottom: 1px solid rgba(0, 0, 0, 0.06);
          padding-bottom: 10px;

          .info-left {
            width: 203px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .info-title {
              display: flex;
              width: 100%;
              color: #000;
              font-size: 14px;
              font-weight: 600;
              line-height: 21px;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
            }
            .info-time {
              display: flex;
              width: 100%;
              font-size: 14px;
              font-weight: 400;
              line-height: 14px;
              color: #bfbfbf;
            }
          }

          .info-right {
            display: flex;
            width: 77px;
            height: 100%;
            align-items: center;

            .info-img {
              display: flex;
              width: 77px;
              height: 60px;
            }
          }
        }
      }
    }

    .swiper-hotel {
      width: 288px !important;
      height: 282px !important;
      margin-right: 20px !important;
    }

    .swiper-con {
      width: 896px;
      height: 325px;

      .card-con {
        display: flex;
        height: 282px;
        width: 100%;
        margin-top: 16px;

        .swiper-width {
          width: 100%;
          border-radius: 12px;
        }

        .hotel {
          width: 288px;
          height: 282px;
          background-color: #fff;
          border-radius: 12px;
          display: flex;
          flex-direction: column;

          .hotel-img {
            width: 288px;
            height: 180px;
            border-radius: 12px;

            .img {
              width: 100%;
              height: 100%;
              border-radius: 12px 12px 0 0;
            }
          }

          .hotel-title {
            padding: 24px 24px;

            .hotel-name {
              color: #000;
              font-size: 18px;
              font-weight: 600;
              line-height: 18px;
            }

            .hotel-address {
              font-family: 'HarmonyLight';
              color: #000;
              font-size: 14px;
              font-weight: 400;
              line-height: 14px;
              margin-top: 10px;
            }
          }
        }
      }
    }

    .life {
      width: 360px;
      height: 325px;

      .life-con {
        width: 360px;
        height: 282px;
        margin-top: 16px;
        border-radius: 12px;

        .life-img {
          width: 360px;
          height: 282px;
          border-radius: 12px;

          .img {
            width: 360px;
            height: 282px;
            border-radius: 12px;
            padding-left: 1px;
            padding-right: 1px;
          }
        }
      }
    }
  }
}

:root {
  font-family: 'HarmonyBold' !important;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}
</style>

<style>

.life .slick-arrow.custom-slick-arrow {
  width: 25px;
  height: 25px;
  font-size: 25px;
  color: #fff;
  background-color: rgba(31, 45, 61, 0.11);
  opacity: 0.8;
  z-index: 1;
}

.banner2 .slick-arrow.custom-slick-arrow {
  width: 36px;
  height: 36px;
  font-size: 36px;
  color: #fff;
  opacity: 0.8;
  z-index: 1;
}

.banner2 .custom-slick-arrow:before,
.life .custom-slick-arrow:before {
  display: none;
}

.swiper-button-prev,
.swiper-button-next {
  color: #fff;
}

.swiper-button-prev:after,
.swiper-button-next:after {
  font-size: 36px;
}

.more-cw .ant-select-selector {
  border: none !important;
  box-shadow: none !important;
}

.more-cw .ant-select-selection-placeholder,
.more-cw .ant-select-selection-item {
  text-align: right;
  margin-right: 5px;
  font-size: 14px !important;
  color: rgba(0, 0, 0, 0.35) !important;
}

.banner-contain-tickets .ant-picker-active-bar {
  bottom: -5px;
}

.ticket-more .ant-select-selector {
  border: none !important;
  box-shadow: none !important;
  height: 22px !important;
  padding: 0px !important;
}

.ticket-more .ant-select-selection-placeholder {
  font-size: 14px !important;
  color: rgba(0, 0, 0, 0.35) !important;
  padding-inline-end: 25px !important;
  line-height: 22px !important;
}

.ticket-more .ant-select-selection-item {
  font-size: 14px !important;
  line-height: 22px !important;
  padding-inline-end: 25px !important;
}

.ticket-more .ant-select-selection-search-input {
  height: 22px !important;
}

.ticket-more .ant-select-show-arrow {
  display: flex;
  align-items: center;
}

.ant-select-item-option-content {
  font-size: 12px;
}
.pointer {
  cursor: pointer;
}

.team-contain {
  width: 100% !important;
  margin: 0 !important;
  .ban-con-title {
    margin-bottom: 15px;
  }
  .ticket-form-bottom{
    display: flex;
            justify-content: space-between;
            padding: 12px 24px;
  }
  .ticket-form-top {
              display: flex;
              justify-content: space-between;
              z-index: 1;
              margin-bottom: 13px;
              padding: 0 30px !important;
              .travel-reason {
                width: 90px;
                height: 24px;
                background: #f5f5f5;
                border-radius: 12px;
                padding: 0 6px;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                font-size: 14px;
                color: #8c8c8c;
                position: relative;
                z-index: 10;
                text-align: center;
                position: relative;
                cursor: pointer;
                .bgc-rea {
                  position: absolute;
                  width: 46px;
                  height: 20px;
                  background: #ffffff;
                  box-shadow: 0px 2px 6px 0px rgba(68, 87, 121, 0.2);
                  border-radius: 11px;
                  top: 2px;
                  left: 2px;
                  cursor: pointer;
                }
                .bgc-rea-o {
                  left: 42px;
                }
                .travel-reason-o {
                  flex: 1;
                  position: relative;
                  z-index: 99;
                }
                .travel-reason-t {
                  flex: 1;
                  position: relative;
                  z-index: 99;
                }
                .travel-reason-oa {
                  color: #3983e5;
                }
              }
            }
  .team-content {
  padding: 30px 0 !important;

    height: 170px !important;
    width: 100% !important;
    margin: 0 !important;
  }
}
</style>
 